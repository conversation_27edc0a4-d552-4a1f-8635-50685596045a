import { QueryInterface, DataTypes } from 'sequelize';

export default {
  async up(queryInterface: QueryInterface) {
    // 创建问卷课程关联表
    await queryInterface.createTable('questionnaire_courses', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '关联ID',
      },
      questionnaire_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '问卷ID',
        references: {
          model: 'questionnaires',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      sso_course_id: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '课程ID（来自SSO系统）',
      },
      sso_course_code: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '课程编码（来自SSO系统）',
      },
      sso_course_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '课程名称（来自SSO系统）',
      },
      section_code: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '学段编码',
      },
      section_name: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '学段名称',
      },
      is_enabled: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '是否启用（用于软删除）',
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
      },
    });

    // 创建索引
    await queryInterface.addIndex('questionnaire_courses', ['questionnaire_id'], {
      name: 'idx_questionnaire_courses_questionnaire_id',
    });

    await queryInterface.addIndex('questionnaire_courses', ['sso_course_id'], {
      name: 'idx_questionnaire_courses_sso_course_id',
    });

    await queryInterface.addIndex('questionnaire_courses', ['sso_course_code'], {
      name: 'idx_questionnaire_courses_sso_course_code',
    });

    // 创建复合索引，确保同一问卷下的课程不重复
    await queryInterface.addIndex('questionnaire_courses', ['questionnaire_id', 'sso_course_id'], {
      unique: true,
      name: 'uk_questionnaire_courses_questionnaire_course',
    });
  },

  async down(queryInterface: QueryInterface) {
    // 删除索引
    await queryInterface.removeIndex('questionnaire_courses', 'uk_questionnaire_courses_questionnaire_course');
    await queryInterface.removeIndex('questionnaire_courses', 'idx_questionnaire_courses_sso_course_code');
    await queryInterface.removeIndex('questionnaire_courses', 'idx_questionnaire_courses_sso_course_id');
    await queryInterface.removeIndex('questionnaire_courses', 'idx_questionnaire_courses_questionnaire_id');

    // 删除表
    await queryInterface.dropTable('questionnaire_courses');
  },
};
