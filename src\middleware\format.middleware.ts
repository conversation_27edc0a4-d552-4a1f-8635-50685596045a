import { Middleware, IMiddleware } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';
import { ApiResponse, ApiResponseUtil } from '../common/ApiResponse';

/**
 * 对接口返回的数据统一包装
 */
@Middleware()
export class FormatMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const result = await next();

      // 如果是文件下载响应（检查Content-Type），直接返回，不进行包装
      if (this.isFileDownloadResponse(ctx)) {
        return result;
      }

      // 如果结果已经是标准的ApiResponse格式，直接返回
      if (this.isApiResponse(result)) {
        return result;
      }

      // 如果是null或undefined，返回成功但无数据
      if (result === null || result === undefined) {
        return ApiResponseUtil.success(null);
      }

      // 其他情况，包装为成功响应
      return ApiResponseUtil.success(result);
    };
  }

  /**
   * 判断是否是文件下载响应
   */
  private isFileDownloadResponse(ctx: Context): boolean {
    const contentType = ctx.get('Content-Type');
    const contentDisposition = ctx.get('Content-Disposition');

    // 检查是否是Excel文件下载
    return (
      contentType && (
        contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') ||
        contentType.includes('application/octet-stream')
      )
    ) || (
      contentDisposition && contentDisposition.includes('attachment')
    );
  }

  /**
   * 判断是否已经是ApiResponse格式
   */
  private isApiResponse(obj: any): obj is ApiResponse {
    return (
      obj &&
      typeof obj === 'object' &&
      typeof obj.errCode === 'number' &&
      typeof obj.msg === 'string' &&
      obj.hasOwnProperty.call(obj, 'data')
    );
  }

  /**
   * 匹配API路径
   */
  match(ctx: Context) {
    return ctx.path.indexOf('/api') === 0;
  }

  static getName(): string {
    return 'API_RESPONSE_FORMAT';
  }
}
