import { QueryInterface, DataTypes } from 'sequelize';

export default {
  async up(queryInterface: QueryInterface): Promise<void> {
    // 添加年级编号字段
    await queryInterface.addColumn('responses', 'grade_code', {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '年级编号',
    });

    // 添加班级编号字段
    await queryInterface.addColumn('responses', 'class_code', {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '班级编号',
    });

    // 添加年级编号索引
    await queryInterface.addIndex('responses', ['grade_code'], {
      name: 'idx_response_grade',
    });

    // 添加班级编号索引
    await queryInterface.addIndex('responses', ['class_code'], {
      name: 'idx_response_class',
    });

    // 添加年级班级组合索引
    await queryInterface.addIndex('responses', ['grade_code', 'class_code'], {
      name: 'idx_response_grade_class',
    });
  },

  async down(queryInterface: QueryInterface): Promise<void> {
    // 删除索引
    await queryInterface.removeIndex('responses', 'idx_response_grade_class');
    await queryInterface.removeIndex('responses', 'idx_response_class');
    await queryInterface.removeIndex('responses', 'idx_response_grade');

    // 删除字段
    await queryInterface.removeColumn('responses', 'class_code');
    await queryInterface.removeColumn('responses', 'grade_code');
  },
};
