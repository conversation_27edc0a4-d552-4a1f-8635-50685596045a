import { Rule, RuleType } from '@midwayjs/validate';
import { ParentInfo } from '../service/api_sso/interface';

/**
 * 验证家长手机号DTO
 */
export class VerifyParentPhoneDTO {
  @Rule(
    RuleType.string()
      .required()
      .pattern(/^1[3-9]\d{9}$/)
      .messages({
        'string.empty': '家长手机号不能为空',
        'string.pattern.base': '家长手机号格式不正确',
        'any.required': '家长手机号是必填项',
      })
  )
  phone: string;
}

/**
 * 家长手机号验证响应接口
 */
export interface IParentPhoneVerifyResponse {
  is_valid: boolean;
  message: string;
  parent?: ParentInfo;
}

/**
 * 查询学生问卷DTO
 */
export class StudentQuestionnairesDTO {
  @Rule(
    RuleType.string().required().messages({
      'string.empty': '学校编码不能为空',
      'any.required': '学校编码是必填项',
    })
  )
  sso_school_code: string;

  @Rule(
    RuleType.string().required().messages({
      'string.empty': '学生ID不能为空',
      'any.required': '学生ID是必填项',
    })
  )
  sso_student_code: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^1[3-9]\d{9}$/)
      .messages({
        'string.pattern.base': '家长手机号格式不正确',
      })
  )
  parent_phone?: string;
}

/**
 * 学生问卷查询响应接口
 */
export interface IStudentQuestionnairesResponse {
  has_questionnaire: boolean;
  message: string;
  questionnaire?: {
    id: number;
    title: string;
    description?: string;
    month: string;
    star_mode: number;
    include_school_evaluation: boolean;
    sso_school_code: string;
    sso_school_name?: string;
    start_time?: Date;
    end_time?: Date;
    instructions?: string;
    max_teachers_limit: number;
    is_submitted: boolean;
  };
}
