# 问卷学科过滤功能

## 功能概述

问卷学科过滤功能允许管理员在创建问卷时选择特定的课程，只有教授这些课程的老师才会出现在问卷的评价选项中。如果不选择任何课程，则表示无需过滤，所有老师都会出现在评价选项中。

## 功能特性

- ✅ 问卷创建时支持选择课程
- ✅ 问卷更新时支持修改课程选择
- ✅ 根据课程配置过滤教师列表
- ✅ 向下兼容：无课程配置时显示所有教师
- ✅ 支持多课程选择
- ✅ 课程信息来源于SSO系统

## 数据模型

### 问卷课程关联表 (questionnaire_courses)

```sql
CREATE TABLE questionnaire_courses (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
  questionnaire_id INT NOT NULL COMMENT '问卷ID',
  sso_course_id VARCHAR(50) NOT NULL COMMENT '课程ID（来自SSO系统）',
  sso_course_code VARCHAR(50) NOT NULL COMMENT '课程编码（来自SSO系统）',
  sso_course_name VARCHAR(100) NOT NULL COMMENT '课程名称（来自SSO系统）',
  section_code VARCHAR(50) COMMENT '学段编码',
  section_name VARCHAR(100) COMMENT '学段名称',
  is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  FOREIGN KEY (questionnaire_id) REFERENCES questionnaires(id) ON DELETE CASCADE,
  UNIQUE KEY uk_questionnaire_course (questionnaire_id, sso_course_id)
);
```

## API 接口

### 1. 创建问卷（支持课程选择）

**接口地址：** `POST /api/questionnaire`

**请求参数：**
```json
{
  "title": "2024年1月教师评价问卷",
  "description": "请对本月教师表现进行评价",
  "month": "2024-01",
  "sso_school_code": "school_001",
  "star_mode": 5,
  "include_school_evaluation": true,
  "selected_courses": [
    {
      "sso_course_id": "course_001",
      "sso_course_code": "C001",
      "sso_course_name": "语文",
      "section_code": "primary",
      "section_name": "小学"
    },
    {
      "sso_course_id": "course_002",
      "sso_course_code": "C002",
      "sso_course_name": "数学",
      "section_code": "primary",
      "section_name": "小学"
    }
  ]
}
```

**说明：**
- `selected_courses` 为可选字段
- 如果不传递或传递空数组，表示不进行课程过滤
- 课程信息需要从 `/public/courses/school/{schoolCode}` 接口获取

### 2. 更新问卷（支持课程选择修改）

**接口地址：** `PUT /api/questionnaire/{id}`

**请求参数：**
```json
{
  "title": "更新后的问卷标题",
  "selected_courses": [
    {
      "sso_course_id": "course_003",
      "sso_course_code": "C003",
      "sso_course_name": "英语",
      "section_code": "primary",
      "section_name": "小学"
    }
  ]
}
```

**说明：**
- 只有草稿状态的问卷才能修改课程选择
- 传递 `selected_courses` 会完全替换现有的课程关联
- 传递空数组会清除所有课程关联

### 3. 获取问卷关联的课程列表

**接口地址：** `GET /api/questionnaire/{id}/courses`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取问卷关联课程成功",
  "data": [
    {
      "sso_course_id": "course_001",
      "sso_course_code": "C001",
      "sso_course_name": "语文",
      "section_code": "primary",
      "section_name": "小学",
      "is_enabled": true
    }
  ]
}
```

### 4. 获取过滤后的教师列表

**接口地址：** `GET /api/questionnaire/{id}/filtered-teachers`

**查询参数：**
- `enterpriseCode`: 学校编码（必填）
- `gradeCode`: 年级编码（必填）
- `classCode`: 班级编码（必填）

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取过滤后教师列表成功",
  "data": [
    {
      "id": "teacher_001",
      "name": "张老师",
      "subject": "语文",
      "memberType": "语文教师",
      "employment_status": "在岗"
    }
  ]
}
```

## 使用流程

### 管理员创建问卷

1. **获取学校课程列表**
   ```javascript
   GET /public/courses/school/{schoolCode}
   ```

2. **创建问卷并选择课程**
   ```javascript
   POST /api/questionnaire
   {
     // 基本问卷信息
     "title": "...",
     "month": "2024-01",
     // 选择的课程
     "selected_courses": [...]
   }
   ```

3. **查看问卷关联的课程**
   ```javascript
   GET /api/questionnaire/{id}/courses
   ```

### 家长填写问卷

1. **获取学生问卷信息**
   ```javascript
   GET /public/parent/student-questionnaires?sso_school_code=...&sso_student_code=...
   ```

2. **获取过滤后的教师列表**
   ```javascript
   GET /api/questionnaire/{id}/filtered-teachers?enterpriseCode=...&gradeCode=...&classCode=...
   ```

3. **提交问卷响应**
   ```javascript
   POST /api/response
   {
     "questionnaire_id": 1,
     "teacher_evaluations": [
       // 只包含过滤后的教师评价
     ]
   }
   ```

## 过滤逻辑

### 教师过滤规则

1. **获取班级所有教师**：通过SSO接口获取指定班级的所有教师
2. **获取问卷课程配置**：查询问卷关联的课程列表
3. **应用过滤规则**：
   - 如果问卷没有配置课程，返回所有教师
   - 如果问卷配置了课程，只返回教授这些课程的教师
4. **匹配逻辑**：
   - 优先匹配教师的 `subject` 字段
   - 备用匹配教师的 `memberType` 字段
   - 使用包含匹配（支持部分匹配）

### 容错处理

- 如果过滤过程出错，会降级返回所有教师，确保功能可用
- 记录错误日志便于排查问题
- 对于无法确定学科的教师，暂时包含在结果中

## 注意事项

1. **数据一致性**：课程信息来源于SSO系统，需要确保数据同步
2. **性能考虑**：教师过滤在内存中进行，对于大量教师的情况需要注意性能
3. **向下兼容**：现有问卷如果没有课程配置，会显示所有教师
4. **权限控制**：只有草稿状态的问卷才能修改课程配置
5. **事务处理**：问卷和课程关联的创建/更新在同一事务中进行

## 测试用例

参考 `src/test/questionnaire-course-filter.test.ts` 文件中的测试用例，包括：

- 创建带课程过滤的问卷
- 创建不带课程过滤的问卷
- 更新问卷的课程配置
- 获取过滤后的教师列表
