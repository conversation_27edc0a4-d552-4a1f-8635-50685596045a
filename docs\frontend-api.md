# 前端API文档 - 统计分析模块

## 基础信息

- **Base URL**: `http://localhost:3141`
- **认证**: JWT <PERSON> (Header: `Authorization: Bearer <token>`)
- **响应格式**: JSON

## 统一响应格式

```json
{
  "errCode": 0,           // 0=成功，非0=失败
  "msg": "操作成功",       // 响应消息
  "data": {},             // 响应数据
  "timestamp": 1705312200000
}
```

## 1. 学校统计分析

### 获取学校统计数据

**接口**: `GET /api/statistics/school`

**参数**:
```typescript
{
  sso_school_code: string;    // 必填，学校编码
  month?: string;             // 可选，月份 YYYY-MM
  start_month?: string;       // 可选，开始月份
  end_month?: string;         // 可选，结束月份
  include_trend?: boolean;    // 可选，是否包含趋势数据
  include_teacher_ranking?: boolean; // 可选，是否包含教师排名
}
```

**响应**:
```typescript
{
  sso_school_code: string;
  sso_school_name: string;
  month: string;
  total_responses: number;        // 总响应数
  completed_responses: number;    // 已完成响应数
  total_students: number;         // 学生总数 (新增)
  completion_rate: number;        // 完成率 (基于学生总数计算)
  school_average_score: number;   // 学校平均分
  teacher_average_score: number;  // 教师平均分
  total_teachers_evaluated: number; // 被评价教师数
  incomplete_students_summary?: { // 未填写学生统计 (新增)
    total_incomplete: number;
    by_grade: Array<{
      grade_code: string;
      grade_name: string;
      incomplete_count: number;
      total_students: number;
      completion_rate: number;
    }>;
    by_class: Array<{
      grade_code: string;
      grade_name: string;
      class_code: string;
      class_name: string;
      incomplete_count: number;
      total_students: number;
      completion_rate: number;
      students: Array<{
        sso_student_code: string;
        sso_student_name: string;
        grade_code: string;
        grade_name: string;
        class_code: string;
        class_name: string;
      }>;
    }>;
  };
}
```

## 2. 未填写学生名单

### 获取未填写学生统计（分页）

**接口**: `GET /api/statistics/incomplete-students`

**参数**:
```typescript
{
  sso_school_code: string;    // 必填，学校编码
  questionnaire_id?: number;  // 可选，问卷ID
  month?: string;             // 可选，月份 YYYY-MM
  page?: number;              // 可选，页码，默认1
  pageSize?: number;          // 可选，每页数量，默认20，最大100
  grade_code?: string;        // 可选，年级编码筛选
  class_code?: string;        // 可选，班级编码筛选
}
```

**响应**:
```typescript
{
  summary: {                  // 统计汇总信息
    total_incomplete: number; // 未填写学生总数
    by_grade: Array<{        // 按年级统计
      grade_code: string;
      grade_name: string;
      incomplete_count: number;
      total_students: number;
      completion_rate: number;
    }>;
    by_class: Array<{        // 所有班级统计（不分页）
      grade_code: string;
      grade_name: string;
      class_code: string;
      class_name: string;
      incomplete_count: number;
      total_students: number;
      completion_rate: number;
      students: Array<{       // 未填写学生列表
        sso_student_code: string;
        sso_student_name: string;
        grade_code: string;
        grade_name: string;
        class_code: string;
        class_name: string;
      }>;
    }>;
  };
  pagination: {               // 分页信息
    page: number;             // 当前页码
    pageSize: number;         // 每页数量
    total: number;            // 总记录数（班级数）
    totalPages: number;       // 总页数
  };
  classes: Array<{            // 当前页的班级数据
    grade_code: string;
    grade_name: string;
    class_code: string;
    class_name: string;
    incomplete_count: number;
    total_students: number;
    completion_rate: number;
    students: Array<{         // 未填写学生列表
      sso_student_code: string;
      sso_student_name: string;
      grade_code: string;
      grade_name: string;
      class_code: string;
      class_name: string;
    }>;
  }>;
}
```

## 3. 教师统计分析

### 获取教师统计数据

**接口**: `GET /api/statistics/teacher`

**参数**:
```typescript
{
  sso_teacher_id: string;     // 必填，教师ID
  sso_school_code?: string;   // 可选，学校编码
  month?: string;             // 可选，月份
  include_distribution?: boolean; // 可选，是否包含评分分布
  include_keywords?: boolean;     // 可选，是否包含关键词云
  include_trend?: boolean;        // 可选，是否包含趋势数据
}
```

### 获取教师排名

**接口**: `GET /api/statistics/teacher-ranking`

**参数**:
```typescript
{
  sso_school_code: string;    // 必填，学校编码
  month?: string;             // 可选，月份
  subject?: string;           // 可选，科目筛选
  department?: string;        // 可选，部门筛选
  page?: number;              // 可选，页码，默认1
  limit?: number;             // 可选，每页数量，默认20
  sort_by?: 'average_score' | 'evaluation_count' | 'recommendation_rate'; // 排序字段
  sort_order?: 'ASC' | 'DESC'; // 排序方向
}
```

## 4. 趋势分析

### 获取趋势分析数据

**接口**: `GET /api/statistics/trend`

**参数**:
```typescript
{
  sso_school_code: string;    // 必填，学校编码
  start_month: string;        // 必填，开始月份 YYYY-MM
  end_month: string;          // 必填，结束月份 YYYY-MM
  sso_teacher_id?: string;    // 可选，教师ID
  analysis_type?: 'school' | 'teacher'; // 分析类型，默认school
}
```

## 使用示例

### JavaScript/TypeScript

```typescript
// 获取学校统计（包含未填写学生）
const getSchoolStats = async (schoolCode: string, month: string) => {
  const response = await fetch(
    `/api/statistics/school?sso_school_code=${schoolCode}&month=${month}`,
    {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    }
  );
  const result = await response.json();
  return result.data;
};

// 获取未填写学生名单（分页）
const getIncompleteStudents = async (
  schoolCode: string,
  options: {
    month?: string;
    page?: number;
    pageSize?: number;
    grade_code?: string;
    class_code?: string;
  } = {}
) => {
  const params = new URLSearchParams({ sso_school_code: schoolCode });
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined) {
      params.append(key, value.toString());
    }
  });

  const response = await fetch(
    `/api/statistics/incomplete-students?${params}`,
    {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    }
  );
  const result = await response.json();
  return result.data;
};

// 获取教师排名
const getTeacherRanking = async (schoolCode: string, options = {}) => {
  const params = new URLSearchParams({ 
    sso_school_code: schoolCode,
    ...options 
  });
  
  const response = await fetch(
    `/api/statistics/teacher-ranking?${params}`,
    {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    }
  );
  const result = await response.json();
  return result.data;
};
```

### React Hook 示例

```typescript
import { useState, useEffect } from 'react';

export const useSchoolStats = (schoolCode: string, month?: string) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const params = new URLSearchParams({ sso_school_code: schoolCode });
        if (month) params.append('month', month);
        
        const response = await fetch(`/api/statistics/school?${params}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });
        
        const result = await response.json();
        if (result.errCode === 0) {
          setData(result.data);
        } else {
          setError(result.msg);
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (schoolCode) {
      fetchData();
    }
  }, [schoolCode, month]);

  return { data, loading, error };
};
```

## 错误处理

### 常见错误码

- `400`: 参数错误
- `401`: 未授权
- `404`: 资源不存在
- `500`: 服务器错误

### 错误响应格式

```json
{
  "errCode": 400,
  "msg": "学校编码不能为空",
  "data": null,
  "timestamp": 1705312200000
}
```

## 重要说明

1. **完成率计算**: 现在基于学校学生总数计算，不再永远是100%
2. **学生数据**: 包含详细的未填写学生名单，按年级班级分组
3. **权限控制**: 所有接口都需要JWT认证
4. **数据实时性**: 统计数据基于最新的问卷响应数据
5. **性能考虑**: 大数据量时建议使用分页和筛选参数
