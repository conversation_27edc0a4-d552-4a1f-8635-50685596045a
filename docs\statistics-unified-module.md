# 教师评价问卷统计分析模块（整合版）

## 📋 概述

本模块实现了教师评价问卷系统的完整统计分析功能，提供学校维度和教师维度的多层次数据分析。系统采用双模式设计：**实时统计**和**缓存统计**，以满足不同场景的性能需求。

## ✨ 核心功能

### 🏫 学校维度统计
- **整体概况分析**：学校平均分、完成率、响应数量统计
- **趋势分析**：按月份展示评分趋势变化
- **教师排名**：基于平均分的教师排行榜
- **年级班级统计**：按年级和班级的完成率分析
- **未填写学生统计**：详细的未填写学生名单

### 👨‍🏫 教师维度统计
- **个人评价分析**：平均分、推荐率、评价数量
- **评分分布**：90-100分、80-89分等各分数段占比
- **关键词云**：从评价描述中提取关键词
- **细分评分**：教学质量、教学态度、课堂管理等维度
- **趋势跟踪**：教师个人评分变化趋势

### 📊 高级分析功能
- **多维度筛选**：支持按学校、月份、科目、部门筛选
- **灵活排序**：支持按平均分、评价数量、推荐率排序
- **性能优化**：使用原生SQL查询，支持大数据量分析
- **缓存机制**：支持统计结果缓存，提升查询性能

## 🏗️ 系统架构

### 双模式设计

#### 1. 实时统计模式
- **适用场景**：数据量较小、需要最新数据的场景
- **特点**：实时计算、数据最新、响应较慢
- **API路径**：`/api/statistics/*`

#### 2. 缓存统计模式
- **适用场景**：数据量较大、对性能要求高的场景
- **特点**：预计算缓存、响应快速、需手动触发更新
- **API路径**：`/api/statistics/*` (trigger, status, cached, incomplete-students)

### 核心组件

```
src/
├── controller/
│   └── statistics.controller.ts        # 统一统计控制器
├── service/
│   └── statistics.service.ts           # 统计服务（整合版）
├── dto/
│   └── statistics.dto.ts               # 统计DTO
├── entity/
│   ├── questionnaire-statistics.entity.ts    # 统计缓存表
│   └── incomplete-students-cache.entity.ts   # 未填写学生缓存表
└── interface.ts                        # 接口定义
```

## 🔧 API 接口

### 实时统计API

#### 1. 获取学校维度统计
```
GET /api/statistics/school
参数：
- sso_school_code: 学校ID（必填）
- month: 月份 YYYY-MM（可选）
- start_month: 开始月份（可选）
- end_month: 结束月份（可选）
- include_trend: 是否包含趋势数据（可选）
- include_teacher_ranking: 是否包含教师排名（可选）
```

#### 2. 获取教师维度统计
```
GET /api/statistics/teacher
参数：
- sso_teacher_id: 教师ID（必填）
- sso_school_code: 学校ID（可选）
- month: 月份 YYYY-MM（可选）
- include_distribution: 是否包含评分分布（可选）
- include_keywords: 是否包含关键词云（可选）
- include_trend: 是否包含趋势数据（可选）
```

#### 3. 获取教师排名
```
GET /api/statistics/teacher-ranking
参数：
- sso_school_code: 学校ID（必填）
- month: 月份（可选）
- subject: 科目筛选（可选）
- department: 部门筛选（可选）
- page: 页码，默认1
- limit: 每页数量，默认20
- sort_by: 排序字段（average_score/evaluation_count/recommendation_rate）
- sort_order: 排序方向（ASC/DESC）
```

#### 4. 获取趋势分析
```
GET /api/statistics/trend
参数：
- sso_school_code: 学校ID（必填）
- start_month: 开始月份（必填）
- end_month: 结束月份（必填）
- sso_teacher_id: 教师ID（可选，用于教师趋势分析）
- analysis_type: 分析类型（school/teacher）
```

#### 5. 获取未填写学生统计
```
GET /api/statistics/incomplete-students
参数：
- sso_school_code: 学校ID（必填）
- questionnaire_id: 问卷ID（可选）
- month: 月份（可选）
- grade_code: 年级编码（可选）
- class_code: 班级编码（可选）
- page: 页码，默认1
- pageSize: 每页数量，默认20
```

### 缓存统计API

#### 1. 触发统计计算
```
POST /api/statistics/trigger
请求体：
{
  "questionnaire_id": 1
}
```

#### 2. 获取统计状态
```
GET /api/statistics/status/:questionnaireId
```

#### 3. 获取缓存的统计数据
```
GET /api/statistics/cached/:questionnaireId
```

#### 4. 获取缓存的未填写学生列表
```
GET /api/statistics/cached-incomplete-students
参数：
- questionnaire_id: 问卷ID（必填）
- grade_code: 年级编码（可选）
- class_code: 班级编码（可选）
- page: 页码，默认1
- pageSize: 每页数量，默认20
```

## 📊 数据模型

### 学校统计数据结构
```typescript
interface ISchoolStatistics {
  sso_school_code: string;           // 学校ID
  sso_school_name?: string;          // 学校名称
  month?: string;                    // 统计月份
  total_responses: number;           // 总响应数
  completed_responses: number;       // 完成响应数
  completion_rate: number;           // 完成率(%)
  school_average_score: number;      // 学校平均分
  teacher_average_score: number;     // 教师平均分
  total_teachers_evaluated: number;  // 被评价教师总数
  response_trend?: ITrendData[];     // 响应趋势数据
  teacher_ranking?: ITeacherRanking[]; // 教师排名
}
```

### 教师统计数据结构
```typescript
interface ITeacherStatistics {
  sso_teacher_id: string;            // 教师ID
  sso_teacher_name?: string;         // 教师姓名
  sso_teacher_subject?: string;      // 教师科目
  sso_teacher_department?: string;   // 教师部门
  month?: string;                    // 统计月份
  total_evaluations: number;         // 总评价数
  average_score: number;             // 平均分
  recommendation_rate: number;       // 推荐率(%)
  score_distribution?: IScoreDistribution[]; // 评分分布
  keyword_cloud?: IKeywordData[];    // 关键词云
  evaluation_trend?: ITrendData[];   // 评价趋势
  detailed_scores?: IDetailedScores; // 细分评分
}
```

## 🚀 使用指南

### 选择合适的模式

#### 使用实时统计模式的场景：
- 数据量较小（学生数 < 1000）
- 需要最新的实时数据
- 偶尔查询，对响应时间要求不高

#### 使用缓存统计模式的场景：
- 数据量较大（学生数 > 1000）
- 频繁查询统计数据
- 对响应时间要求较高
- 可以接受手动触发更新

### 缓存统计使用流程

1. **触发统计计算**
```javascript
const response = await fetch('/api/statistics/trigger', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ questionnaire_id: 1 })
});
```

2. **查询计算状态**
```javascript
const statusResponse = await fetch('/api/statistics/status/1');
const status = await statusResponse.json();
// status.data.status: 'calculating' | 'completed' | 'failed'
```

3. **获取缓存数据**
```javascript
if (status.data.status === 'completed') {
  const dataResponse = await fetch('/api/statistics/cached/1');
  const cachedData = await dataResponse.json();
}
```

## ⚡ 性能优化

### 数据库优化
```sql
-- 建议的索引
CREATE INDEX idx_response_questionnaire_month ON responses(questionnaire_id, month);
CREATE INDEX idx_answer_teacher_rating ON answers(sso_teacher_id, rating);
CREATE INDEX idx_questionnaire_school_month ON questionnaires(sso_school_code, month);
CREATE INDEX idx_statistics_questionnaire ON questionnaire_statistics(questionnaire_id);
CREATE INDEX idx_incomplete_cache_statistics ON incomplete_students_cache(statistics_id);
```

### 性能对比

| 操作类型 | 实时查询 | 缓存查询 | 性能提升 |
|---------|---------|---------|---------|
| 基础统计 | 3-8秒 | 100-300ms | 10-80倍 |
| 未填写学生列表 | 5-10秒 | 200-500ms | 10-50倍 |
| 年级班级统计 | 2-5秒 | 50-200ms | 10-100倍 |

## 🛠️ 部署配置

### 1. 数据库迁移
```bash
mysql -u username -p database_name < database/migrations/20241226-create-statistics-tables.sql
```

### 2. 环境配置
确保以下配置正确：
- 数据库连接配置
- SSO系统接口配置
- 日志配置

### 3. 首次使用
为每个问卷手动触发一次统计计算：
```bash
curl -X POST http://your-domain/api/statistics/trigger \
  -H "Content-Type: application/json" \
  -d '{"questionnaire_id": 1}'
```

## 🔍 监控与维护

### 日志监控
- 统计计算执行时间
- 错误率和失败原因
- 缓存命中率

### 定期维护
- 清理过期的统计缓存
- 优化数据库索引
- 监控磁盘空间使用

## 📝 注意事项

1. **数据一致性**：缓存统计数据基于触发时的快照，需要手动刷新
2. **计算时间**：大型学校可能需要10-30秒计算时间
3. **存储空间**：统计缓存会占用额外的数据库空间
4. **并发控制**：防止同时计算同一问卷的统计
5. **年级班级编码**：统一使用智能提取算法，确保数据一致性

## 🔗 相关文档

- [问卷管理模块](./questionnaire-module.md)
- [响应提交模块](./response-module.md)
- [API完整参考](./api-complete-reference.md)
- [数据库配置](./database-config.md)
