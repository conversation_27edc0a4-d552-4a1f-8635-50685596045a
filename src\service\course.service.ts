import { Provide, Inject } from '@midwayjs/core';
import { CustomError } from '../error/custom.error';
import { Custome } from './api_sso/custome.service';
import { PlatformService } from './api_sso/platform.service';
import { ICourse, IEnterprise } from './api_sso/interface';

/**
 * 课程服务类
 */
@Provide()
export class CourseService {
  @Inject()
  custome: Custome;

  @Inject()
  platformService: PlatformService;

  /**
   * 获取学校的所有课程列表
   * @param schoolCode 学校编码
   * @returns 课程列表
   */
  async getSchoolCourses(schoolCode: string): Promise<ICourse[]> {
    try {
      // 1. 先查询学校基本信息，获取学段信息
      const schoolInfo: IEnterprise = await this.custome.getEnterpriseByCode(
        schoolCode
      );

      if (!schoolInfo) {
        throw new CustomError('学校信息不存在或学校状态异常');
      }

      // 2. 查询学校的所有课程
      const courseList: ICourse[] = await this.platformService.getCourseList(
        schoolCode
      );

      if (!courseList || !Array.isArray(courseList)) {
        return [];
      }

      // 3. 根据学校学段过滤课程，过滤掉无效课程
      const validCourses = courseList.filter(course => {
        // 过滤条件：
        // - 课程必须有有效的ID和编码
        // - 课程名称不能为空
        // - 学段编码要匹配学校的学段编码
        return (
          course.id &&
          course.code &&
          course.name &&
          course.name.trim() !== '' &&
          course.sectionCode === schoolInfo.section_code
        );
      });

      // 4. 按课程编号排序
      validCourses.sort((a, b) => {
        // 优先按课程编码排序，如果编码相同则按课程名称排序
        if (a.code !== b.code) {
          return a.code.localeCompare(b.code);
        }
        return a.name.localeCompare(b.name);
      });

      return validCourses;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('获取学校课程列表失败：' + error.message);
    }
  }
}
