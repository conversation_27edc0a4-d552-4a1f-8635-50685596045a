import {
  Controller,
  Post,
  Get,
  Inject,
  Body,
  Query,
  Param,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { ParentService } from '../service/parent.service';
import { QuestionnaireService } from '../service/questionnaire.service';
import { ResponseService } from '../service/response.service';
import {
  VerifyParentPhoneDTO,
  StudentQuestionnairesDTO,
} from '../dto/parent.dto';
import {
  SubmitResponseDTO,
  GetResponseForEditDTO,
} from '../dto/response.dto';
import { QueryQuestionnaireDTO } from '../dto/questionnaire.dto';
import { BaseController } from '../common/BaseController';
import { ApiResponseUtil } from '../common/ApiResponse';
import { ParamError } from '../error/custom.error';

@Controller('/api/parent')
export class ParentController extends BaseController {
  @Inject()
  parentService: ParentService;

  @Inject()
  questionnaireService: QuestionnaireService;

  @Inject()
  responseService: ResponseService;

  /**
   * 验证家长手机号
   * 通过查询该手机号下的学生信息来验证手机号是否有效
   */
  @Post('/verify-phone')
  @Validate()
  async verifyPhone(@Body() verifyDto: VerifyParentPhoneDTO) {
    this.ctx.logger.info('家长手机号验证请求', {
      phone: verifyDto.phone,
      client_ip: this.ctx.request.ip || 'unknown',
    });

    const result = await this.parentService.verifyParentPhone(verifyDto.phone);

    // 记录验证结果
    this.ctx.logger.info('家长手机号验证完成', {
      phone: verifyDto.phone,
      is_valid: result.is_valid,
      children_count: result.parent?.children?.length || 0,
    });

    const message = result.is_valid ? '手机号验证成功' : '手机号验证失败';
    return ApiResponseUtil.success(result, message);
  }

  /**
   * 查询学生可填写的问卷
   * 根据学校编码、学生ID查询该学生是否有问卷可填写
   */
  @Get('/student-questionnaires')
  @Validate()
  async getStudentQuestionnaires(@Query() queryDto: StudentQuestionnairesDTO) {
    this.ctx.logger.info('查询学生问卷请求', {
      sso_school_code: queryDto.sso_school_code,
      sso_student_code: queryDto.sso_student_code,
      parent_phone: queryDto.parent_phone,
      client_ip: this.ctx.request.ip || 'unknown',
    });

    const result = await this.parentService.getStudentQuestionnaires(
      queryDto.sso_school_code,
      queryDto.sso_student_code,
      queryDto.parent_phone
    );

    // 记录查询结果
    this.ctx.logger.info('查询学生问卷完成', {
      sso_school_code: queryDto.sso_school_code,
      sso_student_code: queryDto.sso_student_code,
      has_questionnaire: result.has_questionnaire,
      is_submitted: result.questionnaire?.is_submitted || false,
      questionnaire_month: result.questionnaire?.month,
    });

    return ApiResponseUtil.success(result, '查询完成');
  }

  /**
   * 获取问卷过滤后的教师列表（家长端）
   * 根据问卷配置的课程过滤教师，供家长端使用
   */
  @Get('/questionnaire/:id/filtered-teachers')
  async getFilteredTeachers(
    @Param('id') questionnaireId: number,
    @Query('enterpriseCode') enterpriseCode: string,
    @Query('gradeCode') gradeCode: string,
    @Query('classCode') classCode: string
  ) {
    this.ctx.logger.info('家长端获取过滤教师列表请求', {
      questionnaireId,
      enterpriseCode,
      gradeCode,
      classCode,
      client_ip: this.ctx.request.ip || 'unknown',
    });

    // 验证问卷是否存在
    const questionnaire = await this.questionnaireService.getQuestionnaireById(
      questionnaireId
    );

    if (!questionnaire) {
      return ApiResponseUtil.error(404, '问卷不存在');
    }

    // 验证必要参数
    if (!enterpriseCode || !gradeCode || !classCode) {
      return ApiResponseUtil.error(
        400,
        '学校编码、年级编码和班级编码都是必填参数'
      );
    }

    const teachers = await this.responseService.getFilteredTeacherList(
      questionnaireId,
      enterpriseCode,
      gradeCode,
      classCode
    );

    // 记录查询结果
    this.ctx.logger.info('家长端获取过滤教师列表完成', {
      questionnaireId,
      enterpriseCode,
      gradeCode,
      classCode,
      teachers_count: teachers?.length || 0,
    });

    return ApiResponseUtil.success(teachers, '获取过滤后教师列表成功');
  }
}
