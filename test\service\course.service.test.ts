import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/service/course.service.test.ts', () => {
  let app: Application;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
    } catch (err) {
      console.error('setup error', err);
      throw err;
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should GET /public/courses/school/:schoolCode', async () => {
    // 测试获取学校课程列表接口
    const result = await createHttpRequest(app)
      .get('/public/courses/school/test_school_001')
      .expect(200);

    console.log('课程列表接口响应:', result.body);
    
    // 验证响应格式
    expect(result.body).toHaveProperty('errCode');
    expect(result.body).toHaveProperty('msg');
    expect(result.body).toHaveProperty('data');
    expect(result.body).toHaveProperty('timestamp');
  });

  it('should handle invalid school code', async () => {
    // 测试无效学校编码
    const result = await createHttpRequest(app)
      .get('/public/courses/school/')
      .expect(404); // 路径参数缺失应该返回404

    console.log('无效学校编码响应:', result.body);
  });

  it('should handle empty school code', async () => {
    // 测试空学校编码
    const result = await createHttpRequest(app)
      .get('/public/courses/school/ ')
      .expect(200);

    console.log('空学校编码响应:', result.body);
    
    // 应该返回错误
    expect(result.body.errCode).not.toBe(0);
    expect(result.body.msg).toContain('学校编码不能为空');
  });
});
