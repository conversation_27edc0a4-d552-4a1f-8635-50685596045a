# 公开接口文档

本文档描述了教师评价问卷系统提供的无需认证的公开接口。

## 接口概述

系统提供了三个公开接口，用于查询问卷信息、教师成绩统计和学校课程信息，这些接口无需JWT认证即可访问。

## 接口列表

### 1. 查询指定学校近1年内的问卷列表

**接口地址：** `GET /public/questionnaire/school/{schoolCode}`

**功能描述：** 获取指定学校近1年内已发布的问卷列表

**请求参数：**
- `schoolCode` (路径参数): 学校编码，必填

**请求示例：**
```
GET /public/questionnaire/school/school_001
```

**响应格式：**
```json
{
  "errCode": 0,
  "msg": "获取问卷列表成功",
  "data": [
    {
      "id": 1,
      "title": "2024年1月教师评价问卷",
      "description": "请对本月教师表现进行评价",
      "month": "2024-01",
      "star_mode": 5,
      "sso_school_code": "school_001",
      "sso_school_name": "示例学校",
      "start_time": "2024-01-01T00:00:00.000Z",
      "end_time": "2024-01-31T23:59:59.000Z",
      "created_at": "2024-01-01T08:00:00.000Z"
    }
  ],
  "timestamp": 1750401123903
}
```

**响应字段说明：**
- `id`: 问卷ID
- `title`: 问卷标题
- `description`: 问卷描述
- `month`: 问卷月份（YYYY-MM格式）
- `star_mode`: 星级模式（5或10）
- `sso_school_code`: 学校编码
- `sso_school_name`: 学校名称
- `start_time`: 问卷开始时间
- `end_time`: 问卷结束时间
- `created_at`: 创建时间

### 2. 获取指定问卷的教师成绩统计

**接口地址：** `GET /public/questionnaire/{questionnaireId}/teacher-scores`

**功能描述：** 获取指定问卷中所有教师的平均成绩统计

**请求参数：**
- `questionnaireId` (路径参数): 问卷ID，必填

**请求示例：**
```
GET /public/questionnaire/1/teacher-scores
```

**响应格式：**
```json
{
  "errCode": 0,
  "msg": "获取教师成绩统计成功",
  "data": [
    {
      "sso_teacher_id": "106",
      "sso_teacher_name": "于涛",
      "sso_teacher_subject": "数学",
      "sso_teacher_department": "数学组",
      "evaluation_count": 25,
      "average_score": 95.5
    },
    {
      "sso_teacher_id": "107",
      "sso_teacher_name": "李明",
      "sso_teacher_subject": "语文",
      "sso_teacher_department": "语文组",
      "evaluation_count": 30,
      "average_score": 92.8
    }
  ],
  "timestamp": 1750401234567
}
```

**响应字段说明：**
- `sso_teacher_id`: 教师SSO ID
- `sso_teacher_name`: 教师姓名
- `sso_teacher_subject`: 教师科目
- `sso_teacher_department`: 教师部门
- `evaluation_count`: 评价次数
- `average_score`: 平均得分（百分制）

**数据排序：** 按平均得分降序排列，得分相同时按评价次数降序排列

### 3. 获取指定学校的所有课程列表

**接口地址：** `GET /public/courses/school/{schoolCode}`

**功能描述：** 获取指定学校的所有有效课程列表，按课程编号排序

**请求参数：**
- `schoolCode` (路径参数): 学校编码，必填

**请求示例：**
```
GET /public/courses/school/school_001
```

**响应格式：**
```json
{
  "errCode": 0,
  "msg": "获取学校课程列表成功",
  "data": [
    {
      "id": "course_001",
      "code": "C001",
      "name": "语文",
      "sectionCode": "primary",
      "sectionName": "小学",
      "buildIn": true,
      "enterpriseCode": "school_001",
      "enterpriseName": "示例小学",
      "semesterCode": "2024_spring",
      "semesterName": "2024年春季学期"
    },
    {
      "id": "course_002",
      "code": "C002",
      "name": "数学",
      "sectionCode": "primary",
      "sectionName": "小学",
      "buildIn": true,
      "enterpriseCode": "school_001",
      "enterpriseName": "示例小学",
      "semesterCode": "2024_spring",
      "semesterName": "2024年春季学期"
    }
  ],
  "timestamp": 1750401345678
}
```

**响应字段说明：**
- `id`: 课程ID
- `code`: 课程编码
- `name`: 课程名称
- `sectionCode`: 学段编码
- `sectionName`: 学段名称
- `buildIn`: 是否内置课程
- `enterpriseCode`: 学校编码
- `enterpriseName`: 学校名称
- `semesterCode`: 学期编码
- `semesterName`: 学期名称

**数据处理逻辑：**
1. 先查询学校基本信息，获取学段信息
2. 查询学校的所有课程
3. 根据学校学段过滤课程，过滤掉无效课程
4. 按课程编号排序返回
5. 一次返回所有数据，不分页

## 错误处理

所有接口都遵循统一的错误响应格式：

```json
{
  "errCode": 400,
  "msg": "错误描述信息",
  "data": null,
  "timestamp": 1750401234567
}
```

**常见错误码：**
- `400`: 业务错误（如参数无效、数据不存在等）
- `404`: 接口不存在
- `500`: 服务器内部错误

## 注意事项

1. **无需认证**: 这些接口无需JWT认证，可直接访问
2. **数据范围**: 只返回已发布状态的问卷数据
3. **时间范围**: 问卷列表接口只返回近1年内的数据
4. **成绩计算**: 教师成绩为所有评价的平均值，使用百分制
5. **数据完整性**: 只统计已完成的问卷响应数据

## 使用示例

### 使用curl命令测试

```bash
# 获取学校问卷列表
curl -X GET "http://localhost:3141/public/questionnaire/school/school_001"

# 获取问卷教师成绩
curl -X GET "http://localhost:3141/public/questionnaire/1/teacher-scores"

# 获取学校课程列表
curl -X GET "http://localhost:3141/public/courses/school/school_001"
```

### 使用JavaScript fetch

```javascript
// 获取学校问卷列表
fetch('/public/questionnaire/school/school_001')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      console.log('问卷列表:', data.data);
    } else {
      console.error('错误:', data.msg);
    }
  });

// 获取教师成绩
fetch('/public/questionnaire/1/teacher-scores')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      console.log('教师成绩:', data.data);
    } else {
      console.error('错误:', data.msg);
    }
  });

// 获取学校课程列表
fetch('/public/courses/school/school_001')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      console.log('课程列表:', data.data);
    } else {
      console.error('错误:', data.msg);
    }
  });
```
