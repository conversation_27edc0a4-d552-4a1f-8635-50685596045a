import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Questionnaire } from './questionnaire.entity';

/**
 * 问卷课程关联实体
 * 用于存储问卷与课程的关联关系，实现学科过滤功能
 */
@Table({
  tableName: 'questionnaire_courses',
  comment: '问卷课程关联表',
  timestamps: true,
  underscored: true,
})
export class QuestionnaireCourse extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '关联ID',
  })
  id?: number = undefined;

  @ForeignKey(() => Questionnaire)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '问卷ID',
  })
  questionnaire_id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '课程ID（来自SSO系统）',
  })
  sso_course_id: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '课程编码（来自SSO系统）',
  })
  sso_course_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '课程名称（来自SSO系统）',
  })
  sso_course_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段编码',
  })
  section_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '学段名称',
  })
  section_name: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用（用于软删除）',
  })
  is_enabled: boolean;

  @CreatedAt
  created_at: Date;

  @UpdatedAt
  updated_at: Date;

  // 关联关系
  @BelongsTo(() => Questionnaire)
  questionnaire: Questionnaire;
}
