import {
  Column,
  Model,
  Table,
  DataType,
  CreatedAt,
  UpdatedAt,
  BelongsTo,
  ForeignKey,
  HasMany,
} from 'sequelize-typescript';
import { Questionnaire } from './questionnaire.entity';
import { GradeStatistics } from './grade-statistics.entity';
import { ClassStatistics } from './class-statistics.entity';
import { TeacherStatistics } from './teacher-statistics.entity';

/**
 * 问卷统计表
 * 用于缓存问卷的统计数据，避免实时计算带来的性能问题
 */
@Table({
  tableName: 'questionnaire_statistics',
  comment: '问卷统计表',
})
export class QuestionnaireStatistics extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '统计ID',
  })
  id: number;

  @ForeignKey(() => Questionnaire)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '问卷ID',
  })
  questionnaire_id: number;

  @BelongsTo(() => Questionnaire)
  questionnaire: Questionnaire;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: 'SSO学校编码',
  })
  sso_school_code: string;

  @Column({
    type: DataType.STRING(7),
    allowNull: false,
    comment: '统计月份 YYYY-MM',
  })
  month: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '学生总数',
  })
  total_students: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '已提交问卷数量',
  })
  submitted_count: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '未提交问卷数量',
  })
  incomplete_count: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0,
    comment: '完成率百分比',
  })
  completion_rate: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true,
    comment: '学校平均评分',
  })
  school_average_score: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true,
    comment: '教师平均评分',
  })
  teacher_average_score: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '参与评价的教师总数',
  })
  total_teachers: number;

  // JSON字段已移除，改为关联表存储

  @Column({
    type: DataType.ENUM('pending', 'calculating', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '统计状态',
  })
  status: 'pending' | 'calculating' | 'completed' | 'failed';

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '最后计算时间',
  })
  last_calculated_at: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '计算耗时(毫秒)',
  })
  calculation_duration: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '错误信息',
  })
  error_message: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '触发统计的用户ID',
  })
  triggered_by: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    comment: '创建时间',
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    comment: '更新时间',
  })
  updated_at: Date;

  // 新增关联关系
  @HasMany(() => GradeStatistics)
  gradeStatistics: GradeStatistics[];

  @HasMany(() => ClassStatistics)
  classStatistics: ClassStatistics[];

  @HasMany(() => TeacherStatistics)
  teacherStatistics: TeacherStatistics[];
}
