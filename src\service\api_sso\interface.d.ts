/**
 * 成员类型信息
 */
export interface MemberType {
  /** 类型ID */
  id: string;
  /** 类型名称 */
  name: string;
  /** 类型编码 */
  code?: string;
}

/**
 * 组织成员信息
 */
export interface OrganizationMember {
  /** 成员ID */
  id: string;
  /** 组织ID */
  organizationId: string;
  /** 成员编码 */
  memberCode: string;
  /** 职位 */
  position?: string;
}

/**
 * 学校平台用户信息
 */
export interface IUserInfo {
  /** 编码 */
  code: string;
  /** 创建时间 */
  createdAt: string;
  /** 电子邮箱 */
  email: null | string;
  /** 就业状态 */
  employment_status: string;
  /** 企业ID */
  enterpriseId: string;
  /** 人脸识别MD5码 */
  facecodeMD5: null | string;
  /** 性别 */
  gender: null | string;
  /** ID */
  id: string;
  /** 是否托管 */
  isTrusteeship: boolean;
  /** 座机号码 */
  landline: null | string;
  /** 成员类型列表 */
  memberTypes: MemberType[];
  /** 手机号码 */
  mobile: null | string;
  /** 姓名 */
  name: string;
  /** 新编码 */
  newCode: string;
  /** 新工号 */
  newWorkCode: string;
  /** 组织成员列表 */
  organizationMembers: OrganizationMember[];
  /** 职位 */
  position?: string;
  /** 更新时间 */
  updatedAt: string;
  /** 用户名 */
  username: null | string;
  /** 工号 */
  work_code: string;
}

export interface IEnterprise {
  id: string;
  code: string;
  name: string;
  english_name: string | null;
  type_code: string;
  type_name: string;
  region: string;
  region_code: string;
  liasion: string;
  mobile: string;
  address: string;
  social_credit_code: string;
  official_url: string | null;
  email: string;
  fax: string | null;
  postal_code: string;
  service_scope: string | null;
  legal_person: string | null;
  legal_person_code: string | null;
  section_name: string;
  section_alias_name: string;
  section_code: string;
  describe: string;
  school_motto: string;
  remark: string | null;
  honor: string;
  theme: string;
  style: string | null;
  logo_url: string;
  business_license_url: string;
  school_license_url: string;
  independent: boolean;
  hosted: boolean;
  hostedTime: string | null;
  showInitMessage: boolean;
  need_improve: boolean;
  teacher_need_improve: boolean;
  smart_screen_type: number;
  createdAt: string;
  updatedAt: string;
}

export interface IClass {
  id: string;
  code: string;
  orderIndex: number;
  name: string;
  grade_code: string;
  grade_name: string;
  site_code: string;
  site_name: string;
  monitor: string | null;
  monitorCode: string | null;
  slogan: string;
  graduation: boolean;
  section_name: string;
  section_code: string;
  stage: string;
  graduation_semester: string;
  memberId: string;
  expectation: string;
  member_code: string | null;
  member_name: string | null;
  semesterCode: string;
  semesterName: string;
  enterpriseId: string;
  'class-student': {
    classId: string;
    studentId: string;
    code: string;
  };
  enterprise: IEnterprise;
}

export interface ParentChild {
  id: string;
  relation: string;
  is_guardian: boolean;
  parentId: string;
  studentId: string;
  student: IStudent;
}

export interface ParentInfo {
  id: string;
  mobile: string;
  code: string;
  name: string;
  avatar: string;
  username: string;
  gender: string | null;
  email: string;
  address: string;
  facecode: string | null;
  facecodeMD5: string | null;
  faceImg: string | null;
  createdAt: string;
  updatedAt: string;
  children: ParentChild[];
}

/**
 * 课程信息
 */
export interface ICourse {
  /** 课程ID */
  id: string;
  /** 课程编码 */
  code: string;
  /** 课程名称 */
  name: string;
  /** 学段编码 */
  sectionCode: string;
  /** 学段名称 */
  sectionName: string;
  /** 是否内置 */
  buildIn: boolean;
  /** 企业编码 */
  enterpriseCode: string;
  /** 企业名称 */
  enterpriseName: string;
  /** 学期编码 */
  semesterCode: string;
  /** 学期名称 */
  semesterName: string;
}

interface IParentStudentRelation {
  /** 关系ID */
  id: string;
  /** 关系类型 */
  relation: string;
  /** 是否监护人 */
  is_guardian: boolean;
  /** 家长ID */
  parentId: string;
  /** 学生ID */
  studentId: string;
}

interface IParent {
  /** 家长ID */
  id: string;
  /** 家长编码 */
  code: string;
  /** 家长姓名 */
  name: string;
  /** 头像 */
  avatar: string;
  /** 用户名 */
  username: string;
  /** 性别 */
  gender: string | null;
  /** 手机号 */
  mobile: string;
  /** 邮箱 */
  email: string | null;
  /** 地址 */
  address: string | null;
  /** 人脸识别码 */
  facecode: string | null;
  /** 人脸识别MD5码 */
  facecodeMD5: string | null;
  /** 人脸图片 */
  faceImg: string | null;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 家长学生关系 */
  parentStudentRelation?: IParentStudentRelation[];
}

/** 学生信息 */
export interface IStudent {
  /** 学生ID */
  id: string;
  /** 学生编码 */
  code: string;
  /** 工号 */
  work_code: string;
  /** 阶段 */
  stage: string;
  /** 毕业学期 */
  graduation_semester: string;
  /** 用户名 */
  username: string | null;
  /** 姓名 */
  name: string;
  /** 头像 */
  avatar: string | null;
  /** 身份证号 */
  IDNumber: string | null;
  /** 性别 */
  gender: string;
  /** 手机号 */
  mobile: string | null;
  /** 邮箱 */
  email: string;
  /** 是否毕业 */
  graduation: boolean;
  /** 毕业日期 */
  graduationDate: string | null;
  /** 状态 */
  status: string;
  /** 备注 */
  remark: string | null;
  /** 户籍地址 */
  household_register: string | null;
  /** 当前住址 */
  current_address: string | null;
  /** 企业ID */
  enterpriseId: string;
  /** 人脸识别MD5码 */
  facecodeMD5: string | null;
  /** 是否需要卡片 */
  isNeedCard: boolean;
  /** 卡片信息 */
  cardInfo: string | null;
  /** 外部卡片信息 */
  outCardInfo: string | null;
  /** 是否更新 */
  isUpdate: boolean;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 班级信息 */
  classes: IClass[];
  /** 家长信息 */
  parents?: IParent[];
}
