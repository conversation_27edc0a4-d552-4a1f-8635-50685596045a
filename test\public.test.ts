import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/public.test.ts', () => {
  let app: Application;

  beforeAll(async () => {
    // create app
    app = await createApp<Framework>();
  });

  afterAll(async () => {
    await close(app);
  });

  it('should GET /public/questionnaire/school/:schoolCode', async () => {
    // 使用一个测试学校编码
    const schoolCode = 'test_school_001';
    
    const result = await createHttpRequest(app)
      .get(`/public/questionnaire/school/${schoolCode}`)
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toBeDefined();
    expect(Array.isArray(result.body.data)).toBe(true);
  });

  it('should GET /public/questionnaire/:questionnaireId/teacher-scores', async () => {
    // 使用一个测试问卷ID
    const questionnaireId = 1;
    
    const result = await createHttpRequest(app)
      .get(`/public/questionnaire/${questionnaireId}/teacher-scores`)
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toBeDefined();
    expect(Array.isArray(result.body.data)).toBe(true);
  });

  it('should return error for invalid school code', async () => {
    await createHttpRequest(app)
      .get('/public/questionnaire/school/')
      .expect(404); // 路径不匹配会返回404
  });

  it('should return error for invalid questionnaire id', async () => {
    const result = await createHttpRequest(app)
      .get('/public/questionnaire/invalid/teacher-scores')
      .expect(200); // 会进入控制器但参数验证失败

    expect(result.body.errCode).not.toBe(0);
  });
});
