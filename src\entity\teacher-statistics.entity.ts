import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { QuestionnaireStatistics } from './questionnaire-statistics.entity';

@Table({
  tableName: 'teacher_statistics',
  comment: '教师统计表',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class TeacherStatistics extends Model<TeacherStatistics> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '主键ID',
  })
  id: number;

  @ForeignKey(() => QuestionnaireStatistics)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '主统计表ID',
  })
  statistics_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '问卷ID',
  })
  questionnaire_id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学校编码',
  })
  sso_school_code: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '教师ID',
  })
  sso_teacher_id: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '教师姓名',
  })
  sso_teacher_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '教师科目',
  })
  sso_teacher_subject: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '教师部门',
  })
  sso_teacher_department: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '评价数量',
  })
  evaluation_count: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总分',
  })
  total_score: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '平均分',
  })
  average_score: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排名',
  })
  ranking: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '创建时间',
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '更新时间',
  })
  updated_at: Date;

  // 关联关系
  @BelongsTo(() => QuestionnaireStatistics)
  statistics: QuestionnaireStatistics;
}
