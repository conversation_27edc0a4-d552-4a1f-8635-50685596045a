/*
 * @Description: JWT组件响应工具类
 * @Date: 2025-05-31
 */

/**
 * 统一响应格式接口
 */
export interface JwtApiResponse<T = any> {
  errCode: number;
  msg: string;
  data: T | null;
  timestamp: number;
}

/**
 * JWT组件响应工具类
 */
export class JwtResponseUtil {
  /**
   * 成功响应
   * @param data 响应数据
   * @param msg 响应消息
   * @param errCode 错误码，默认为0
   */
  static success<T>(data: T, msg = '操作成功', errCode = 0): JwtApiResponse<T> {
    return {
      errCode,
      msg,
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * 错误响应
   * @param errCode 错误码
   * @param msg 错误消息
   * @param data 响应数据，默认为null
   */
  static error<T = null>(
    errCode: number,
    msg: string,
    data: T = null as T
  ): JwtApiResponse<T> {
    return {
      errCode,
      msg,
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * 分页响应
   * @param list 数据列表
   * @param total 总数
   * @param page 当前页
   * @param limit 每页数量
   * @param msg 响应消息
   */
  static page<T>(
    list: T[],
    total: number,
    page: number,
    limit: number,
    msg = '获取数据成功'
  ): JwtApiResponse<{
    list: T[];
    total: number;
    page: number;
    limit: number;
  }> {
    return this.success(
      {
        list,
        total,
        page,
        limit,
      },
      msg
    );
  }
}
