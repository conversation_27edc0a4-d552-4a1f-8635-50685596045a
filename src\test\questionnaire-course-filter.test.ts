import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/questionnaire-course-filter.test.ts', () => {
  let app: Application;
  let httpRequest: any;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
      httpRequest = createHttpRequest(app);
    } catch (err) {
      console.error('setup error', err);
      throw err;
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should create questionnaire with course filter', async () => {
    const questionnaireData = {
      title: '2024年1月教师评价问卷（带课程过滤）',
      description: '测试课程过滤功能',
      month: '2024-01',
      sso_school_code: 'test_school_001',
      star_mode: 5,
      include_school_evaluation: true,
      selected_courses: [
        {
          sso_course_id: 'course_001',
          sso_course_code: 'C001',
          sso_course_name: '语文',
          section_code: 'primary',
          section_name: '小学',
        },
        {
          sso_course_id: 'course_002',
          sso_course_code: 'C002',
          sso_course_name: '数学',
          section_code: 'primary',
          section_name: '小学',
        },
      ],
    };

    const result = await httpRequest
      .post('/api/questionnaire')
      .send(questionnaireData)
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toBeDefined();
    expect(result.body.data.id).toBeDefined();

    // 保存问卷ID用于后续测试
    const questionnaireId = result.body.data.id;

    // 测试获取问卷关联的课程
    const coursesResult = await httpRequest
      .get(`/api/questionnaire/${questionnaireId}/courses`)
      .expect(200);

    expect(coursesResult.body.errCode).toBe(0);
    expect(coursesResult.body.data).toHaveLength(2);
    expect(coursesResult.body.data[0].sso_course_name).toBe('语文');
    expect(coursesResult.body.data[1].sso_course_name).toBe('数学');
  });

  it('should create questionnaire without course filter', async () => {
    const questionnaireData = {
      title: '2024年2月教师评价问卷（无课程过滤）',
      description: '测试无课程过滤功能',
      month: '2024-02',
      sso_school_code: 'test_school_001',
      star_mode: 5,
      include_school_evaluation: true,
      // 不设置 selected_courses
    };

    const result = await httpRequest
      .post('/api/questionnaire')
      .send(questionnaireData)
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toBeDefined();

    const questionnaireId = result.body.data.id;

    // 测试获取问卷关联的课程（应该为空）
    const coursesResult = await httpRequest
      .get(`/api/questionnaire/${questionnaireId}/courses`)
      .expect(200);

    expect(coursesResult.body.errCode).toBe(0);
    expect(coursesResult.body.data).toHaveLength(0);
  });

  it('should update questionnaire with course filter', async () => {
    // 先创建一个问卷
    const questionnaireData = {
      title: '2024年3月教师评价问卷',
      description: '测试更新课程过滤功能',
      month: '2024-03',
      sso_school_code: 'test_school_001',
      star_mode: 5,
      include_school_evaluation: true,
    };

    const createResult = await httpRequest
      .post('/api/questionnaire')
      .send(questionnaireData)
      .expect(200);

    const questionnaireId = createResult.body.data.id;

    // 更新问卷，添加课程过滤
    const updateData = {
      title: '2024年3月教师评价问卷（已更新）',
      selected_courses: [
        {
          sso_course_id: 'course_003',
          sso_course_code: 'C003',
          sso_course_name: '英语',
          section_code: 'primary',
          section_name: '小学',
        },
      ],
    };

    const updateResult = await httpRequest
      .put(`/api/questionnaire/${questionnaireId}`)
      .send(updateData)
      .expect(200);

    expect(updateResult.body.errCode).toBe(0);

    // 验证课程关联已更新
    const coursesResult = await httpRequest
      .get(`/api/questionnaire/${questionnaireId}/courses`)
      .expect(200);

    expect(coursesResult.body.errCode).toBe(0);
    expect(coursesResult.body.data).toHaveLength(1);
    expect(coursesResult.body.data[0].sso_course_name).toBe('英语');
  });

  it('should get filtered teacher list', async () => {
    // 先创建一个带课程过滤的问卷
    const questionnaireData = {
      title: '2024年4月教师评价问卷',
      description: '测试教师过滤功能',
      month: '2024-04',
      sso_school_code: 'test_school_001',
      star_mode: 5,
      include_school_evaluation: true,
      selected_courses: [
        {
          sso_course_id: 'course_001',
          sso_course_code: 'C001',
          sso_course_name: '语文',
          section_code: 'primary',
          section_name: '小学',
        },
      ],
    };

    const createResult = await httpRequest
      .post('/api/questionnaire')
      .send(questionnaireData)
      .expect(200);

    const questionnaireId = createResult.body.data.id;

    // 测试获取过滤后的教师列表
    const teachersResult = await httpRequest
      .get(`/api/questionnaire/${questionnaireId}/filtered-teachers`)
      .query({
        enterpriseCode: 'test_school_001',
        gradeCode: '1',
        classCode: '1',
      })
      .expect(200);

    expect(teachersResult.body.errCode).toBe(0);
    expect(teachersResult.body.data).toBeDefined();
    // 注意：由于这是测试环境，实际的教师数据可能不存在
    // 这里主要测试API是否正常响应
  });
});
