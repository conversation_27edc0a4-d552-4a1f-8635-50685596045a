import { Provide, Scope, ScopeEnum } from '@midwayjs/core';
import { DataListener } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';

@Provide()
@Scope(ScopeEnum.Singleton)
export class FileCleanupListener extends DataListener<{
  lastCleanupTime: Date | null;
  nextCleanupTime: Date | null;
  totalCleaned: number;
  totalSizeCleaned: number;
}> {
  private cleanupTimer: NodeJS.Timeout | null = null;
  private lastCleanupTime: Date | null = null;
  private totalCleaned = 0;
  private totalSizeCleaned = 0;

  private readonly FILE_MAX_AGE = 2 * 60 * 60 * 1000; // 2小时

  // 初始化数据
  initData() {
    console.log('文件清理监听器：初始化');
    const nextCleanupTime = this.calculateNextCleanupTime();
    console.log(
      `文件清理监听器：下次清理时间 ${nextCleanupTime.toLocaleString()}`
    );

    return {
      lastCleanupTime: this.lastCleanupTime,
      nextCleanupTime: nextCleanupTime,
      totalCleaned: this.totalCleaned,
      totalSizeCleaned: this.totalSizeCleaned,
    };
  }

  // 更新数据
  onData(
    setData: (data: {
      lastCleanupTime: Date | null;
      nextCleanupTime: Date | null;
      totalCleaned: number;
      totalSizeCleaned: number;
    }) => void
  ) {
    console.log('文件清理监听器：启动每2小时清理任务');

    // 立即执行一次清理
    this.executeCleanup(setData);

    // 设置每2小时执行的定时器
    this.cleanupTimer = setInterval(() => {
      this.executeCleanup(setData);
    }, 2 * 60 * 60 * 1000); // 2小时
  }

  /**
   * 执行清理任务
   */
  private async executeCleanup(
    setData: (data: {
      lastCleanupTime: Date | null;
      nextCleanupTime: Date | null;
      totalCleaned: number;
      totalSizeCleaned: number;
    }) => void
  ) {
    try {
      console.log('文件清理监听器：开始执行文件清理任务');

      const result = this.cleanupExpiredFiles();

      this.lastCleanupTime = new Date();
      this.totalCleaned += result.deletedCount;
      this.totalSizeCleaned += result.deletedSize;

      const nextCleanupTime = this.calculateNextCleanupTime();

      console.log(
        `文件清理监听器：清理完成，删除了 ${
          result.deletedCount
        } 个文件，释放空间 ${this.formatFileSize(result.deletedSize)}`
      );
      console.log(
        `文件清理监听器：下次清理时间 ${nextCleanupTime.toLocaleString()}`
      );

      // 更新状态
      setData({
        lastCleanupTime: this.lastCleanupTime,
        nextCleanupTime: nextCleanupTime,
        totalCleaned: this.totalCleaned,
        totalSizeCleaned: this.totalSizeCleaned,
      });
    } catch (error) {
      console.error('文件清理监听器：清理任务执行失败', error);
    }
  }

  /**
   * 清理过期的临时文件
   */
  private cleanupExpiredFiles(): { deletedCount: number; deletedSize: number } {
    try {
      const tempDir = path.join(process.cwd(), 'public', 'temp');

      // 检查临时目录是否存在
      if (!fs.existsSync(tempDir)) {
        console.warn('文件清理监听器：临时文件目录不存在', tempDir);
        return { deletedCount: 0, deletedSize: 0 };
      }

      const files = fs.readdirSync(tempDir);
      const now = Date.now();
      let deletedCount = 0;
      let deletedSize = 0;

      files.forEach(filename => {
        const filePath = path.join(tempDir, filename);

        try {
          const stats = fs.statSync(filePath);

          // 检查文件是否为普通文件（排除目录）
          if (!stats.isFile()) {
            return;
          }

          // 计算文件年龄
          const fileAge = now - stats.mtime.getTime();

          // 如果文件超过2小时，则删除
          if (fileAge > this.FILE_MAX_AGE) {
            const fileSize = stats.size;
            fs.unlinkSync(filePath);
            deletedCount++;
            deletedSize += fileSize;

            console.log('文件清理监听器：已删除过期临时文件', {
              filename,
              fileAge: Math.round(fileAge / 1000 / 60), // 转换为分钟
              fileSize: this.formatFileSize(fileSize),
            });
          }
        } catch (error) {
          console.error('文件清理监听器：处理文件时出错', error, {
            filename,
            filePath,
          });
        }
      });

      return { deletedCount, deletedSize };
    } catch (error) {
      console.error('文件清理监听器：清理临时文件时发生错误', error);
      return { deletedCount: 0, deletedSize: 0 };
    }
  }

  /**
   * 计算下次清理时间（当前时间 + 2小时）
   */
  private calculateNextCleanupTime(): Date {
    const now = new Date();
    return new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2小时后
  }

  /**
   * 手动清理文件（供外部调用）
   */
  public manualCleanup(): { deletedCount: number; deletedSize: number } {
    console.log('文件清理监听器：执行手动清理');
    return this.cleanupExpiredFiles();
  }

  /**
   * 获取临时文件目录状态
   */
  public getTempDirStatus(): {
    totalFiles: number;
    totalSize: number;
    expiredFiles: number;
    expiredSize: number;
  } {
    try {
      const tempDir = path.join(process.cwd(), 'public', 'temp');

      if (!fs.existsSync(tempDir)) {
        return { totalFiles: 0, totalSize: 0, expiredFiles: 0, expiredSize: 0 };
      }

      const files = fs.readdirSync(tempDir);
      const now = Date.now();

      let totalFiles = 0;
      let totalSize = 0;
      let expiredFiles = 0;
      let expiredSize = 0;

      files.forEach(filename => {
        const filePath = path.join(tempDir, filename);

        try {
          const stats = fs.statSync(filePath);

          if (stats.isFile()) {
            totalFiles++;
            totalSize += stats.size;

            const fileAge = now - stats.mtime.getTime();
            if (fileAge > this.FILE_MAX_AGE) {
              expiredFiles++;
              expiredSize += stats.size;
            }
          }
        } catch (error) {
          console.error('文件清理监听器：获取文件状态失败', error, {
            filename,
          });
        }
      });

      return { totalFiles, totalSize, expiredFiles, expiredSize };
    } catch (error) {
      console.error('文件清理监听器：获取临时目录状态失败', error);
      return { totalFiles: 0, totalSize: 0, expiredFiles: 0, expiredSize: 0 };
    }
  }

  /**
   * 删除指定文件
   */
  public deleteFile(filename: string): boolean {
    try {
      const filePath = path.join(process.cwd(), 'public', 'temp', filename);

      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        fs.unlinkSync(filePath);

        console.log('文件清理监听器：手动删除临时文件', {
          filename,
          fileSize: this.formatFileSize(stats.size),
        });

        return true;
      } else {
        console.warn('文件清理监听器：要删除的文件不存在', { filename });
        return false;
      }
    } catch (error) {
      console.error('文件清理监听器：删除文件失败', error, { filename });
      return false;
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 清理资源
   */
  async destroyListener() {
    console.log('文件清理监听器：清理资源');

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    console.log('文件清理监听器：资源清理完成');
  }
}
