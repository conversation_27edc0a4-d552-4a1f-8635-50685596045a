import { Controller, Get, Inject, Param, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Custome } from '../service/api_sso/custome.service';
import { ApiResponseUtil } from '../common/ApiResponse';

@Controller('/api/sso')
export class SSOController {
  @Inject()
  ctx: Context;

  @Inject()
  custome: Custome;

  /**
   * 获取指定班级的教师列表
   * @param enterpriseCode 学校编号
   * @param gradeCode 年级编号
   * @param classCode 班级编号
   */
  @Get('/teachers/:enterpriseCode')
  async getTeacherListForClass(
    @Param('enterpriseCode') enterpriseCode: string,
    @Query('gradeCode') gradeCode: string,
    @Query('classCode') classCode: string
  ) {
    try {
      this.ctx.logger.info('开始获取班级教师列表', {
        enterpriseCode,
        gradeCode,
        classCode,
      });

      const result = await this.custome.getTeacherListForClass(
        enterpriseCode,
        gradeCode,
        classCode
      );

      this.ctx.logger.info('成功获取班级教师列表', {
        enterpriseCode,
        gradeCode,
        classCode,
        teacherCount: result?.length || 0,
      });

      return ApiResponseUtil.success(result, '获取班级教师列表成功');
    } catch (error) {
      this.ctx.logger.error('获取班级教师列表失败', {
        enterpriseCode,
        gradeCode,
        classCode,
        error: error.message,
      });

      return ApiResponseUtil.error(400, error.message);
    }
  }

  /**
   * 获取学校的所有成员列表
   * @param enterpriseCode 学校编号
   */
  @Get('/members/:enterpriseCode')
  async getMembers(@Param('enterpriseCode') enterpriseCode: string) {
    try {
      this.ctx.logger.info('开始获取成员列表', { enterpriseCode });

      const result = await this.custome.getMembers(enterpriseCode);

      this.ctx.logger.info('成功获取成员列表', {
        enterpriseCode,
        count: result.count,
        listLength: result.list?.length || 0,
      });

      return ApiResponseUtil.success(result, '获取成员列表成功');
    } catch (error) {
      this.ctx.logger.error('获取成员列表失败', {
        enterpriseCode,
        error: error.message,
      });

      return ApiResponseUtil.error(400, error.message);
    }
  }

  /**
   * 根据成员编号获取成员详细信息
   * @param enterpriseCode 学校编号
   * @param memberCode 成员编号
   */
  @Get('/member/:enterpriseCode/:memberCode')
  async getMemberByCode(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('memberCode') memberCode: string
  ) {
    try {
      this.ctx.logger.info('开始获取指定成员信息', {
        enterpriseCode,
        memberCode,
      });

      const result = await this.custome.getMemberByCode(
        enterpriseCode,
        memberCode
      );

      this.ctx.logger.info('成功获取指定成员信息', {
        enterpriseCode,
        memberCode,
        memberName: result?.name || 'unknown',
      });

      return ApiResponseUtil.success(result, '获取成员信息成功');
    } catch (error) {
      this.ctx.logger.error('获取成员信息失败', {
        enterpriseCode,
        memberCode,
        error: error.message,
      });

      return ApiResponseUtil.error(400, error.message);
    }
  }

  /**
   * 获取学校信息
   * @param enterpriseCode 学校编号
   */
  @Get('/enterprise/:enterpriseCode')
  async getEnterpriseByCode(@Param('enterpriseCode') enterpriseCode: string) {
    try {
      this.ctx.logger.info('开始获取学校信息', { enterpriseCode });

      const result = await this.custome.getEnterpriseByCode(enterpriseCode);

      this.ctx.logger.info('成功获取学校信息', {
        enterpriseCode,
        schoolName: result?.name || 'unknown',
      });

      return ApiResponseUtil.success(result, '获取学校信息成功');
    } catch (error) {
      this.ctx.logger.error('获取学校信息失败', {
        enterpriseCode,
        error: error.message,
      });

      return ApiResponseUtil.error(400, error.message);
    }
  }

  /**
   * 获取学校角色列表
   * @param enterpriseCode 学校编号
   */
  @Get('/roles/:enterpriseCode')
  async getSchoolRoles(@Param('enterpriseCode') enterpriseCode: string) {
    try {
      this.ctx.logger.info('开始获取学校角色列表', { enterpriseCode });

      const result = await this.custome.getSchoolRoles(enterpriseCode);

      this.ctx.logger.info('成功获取学校角色列表', {
        enterpriseCode,
        roleCount: result?.list?.length || 0,
      });

      return ApiResponseUtil.success(result, '获取学校角色列表成功');
    } catch (error) {
      this.ctx.logger.error('获取学校角色列表失败', {
        enterpriseCode,
        error: error.message,
      });

      return ApiResponseUtil.error(400, error.message);
    }
  }

  /**
   * 获取指定角色下的用户列表
   * @param enterpriseCode 学校编号
   * @param roleCode 角色编号
   * @param limit 每页数量
   * @param offset 页码
   */
  @Get('/role-users/:enterpriseCode/:roleCode')
  async getSchoolUserRoleList(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('roleCode') roleCode: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number
  ) {
    try {
      this.ctx.logger.info('开始获取角色用户列表', {
        enterpriseCode,
        roleCode,
        limit,
        offset,
      });

      const result = await this.custome.getSchoolUserRoleList(
        enterpriseCode,
        roleCode,
        limit,
        offset
      );

      this.ctx.logger.info('成功获取角色用户列表', {
        enterpriseCode,
        roleCode,
        userCount: result?.count || 0,
      });

      return ApiResponseUtil.success(result, '获取角色用户列表成功');
    } catch (error) {
      this.ctx.logger.error('获取角色用户列表失败', {
        enterpriseCode,
        roleCode,
        error: error.message,
      });

      return ApiResponseUtil.error(400, error.message);
    }
  }

  /**
   * 获取学期列表
   * @param enterpriseCode 学校编号
   */
  @Get('/semesters/:enterpriseCode')
  async getSemesterList(@Param('enterpriseCode') enterpriseCode: string) {
    try {
      this.ctx.logger.info('开始获取学期列表', { enterpriseCode });

      const result = await this.custome.getSemesterList(enterpriseCode);

      this.ctx.logger.info('成功获取学期列表', {
        enterpriseCode,
        semesterCount: result?.length || 0,
      });

      return ApiResponseUtil.success(result, '获取学期列表成功');
    } catch (error) {
      this.ctx.logger.error('获取学期列表失败', {
        enterpriseCode,
        error: error.message,
      });

      return ApiResponseUtil.error(400, error.message);
    }
  }

  /**
   * 测试获取班级教师列表 - 尝试多种年级班级组合
   * @param enterpriseCode 学校编号
   */
  @Get('/test-teachers/:enterpriseCode')
  async testTeacherListForClass(
    @Param('enterpriseCode') enterpriseCode: string
  ) {
    const testCombinations = [
      { gradeCode: '1', classCode: '1' },
      { gradeCode: '1', classCode: '2' },
      { gradeCode: '2', classCode: '1' },
      { gradeCode: '3', classCode: '1' },
      { gradeCode: '4', classCode: '1' },
      { gradeCode: '5', classCode: '1' },
      { gradeCode: '6', classCode: '1' },
      { gradeCode: '01', classCode: '01' },
      { gradeCode: '02', classCode: '01' },
      { gradeCode: '一年级', classCode: '1班' },
      { gradeCode: '一', classCode: '一' },
    ];

    const results = [];

    for (const combination of testCombinations) {
      try {
        this.ctx.logger.info('测试年级班级组合', {
          enterpriseCode,
          ...combination,
        });

        const result = await this.custome.getTeacherListForClass(
          enterpriseCode,
          combination.gradeCode,
          combination.classCode
        );

        results.push({
          gradeCode: combination.gradeCode,
          classCode: combination.classCode,
          success: true,
          teacherCount: result?.length || 0,
          data: result,
        });

        this.ctx.logger.info('年级班级组合测试成功', {
          enterpriseCode,
          ...combination,
          teacherCount: result?.length || 0,
        });

        // 如果找到了数据，就不继续测试了
        if (result && result.length > 0) {
          break;
        }
      } catch (error) {
        results.push({
          gradeCode: combination.gradeCode,
          classCode: combination.classCode,
          success: false,
          error: error.message,
        });

        this.ctx.logger.warn('年级班级组合测试失败', {
          enterpriseCode,
          ...combination,
          error: error.message,
        });
      }
    }

    return ApiResponseUtil.success(results, '班级教师列表测试完成');
  }
}
