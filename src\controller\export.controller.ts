import { Controller, Post, Inject, Body } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { ExportService } from '../service/export.service';
import {
  BaseExportDTO,
  SchoolStatisticsExportDTO,
  TeacherStatisticsExportDTO,
  TeacherRankingExportDTO,
  QuestionnaireResponsesExportDTO,
  ComprehensiveReportExportDTO,
  IncompleteStudentsExportDTO,
  ExportType,
} from '../dto/export.dto';
import { ErrorCode } from '../common/ErrorCode';

@Controller('/api/export')
export class ExportController {
  @Inject()
  ctx: Context;

  @Inject()
  exportService: ExportService;

  /**
   * 导出学校统计数据
   */
  @Post('/school-statistics')
  @Validate()
  async exportSchoolStatistics(@Body() exportDto: SchoolStatisticsExportDTO) {
    try {
      this.ctx.logger.info('导出学校统计数据请求', {
        sso_school_code: exportDto.sso_school_code,
        month: exportDto.month,
        export_format: exportDto.export_format,
      });

      exportDto.export_type = ExportType.SCHOOL_STATISTICS;
      const result = await this.exportService.exportToExcel(exportDto);

      // 返回下载链接
      this.ctx.body = {
        errCode: 0,
        msg: '导出成功',
        data: {
          downloadUrl: result.downloadUrl,
          filename: result.filename,
          fileId: result.fileId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('导出学校统计数据失败', error, {
        sso_school_code: exportDto.sso_school_code,
      });

      this.ctx.status = 500;
      this.ctx.body = {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '导出失败',
        data: null,
      };
    }
  }

  /**
   * 导出教师统计数据
   */
  @Post('/teacher-statistics')
  @Validate()
  async exportTeacherStatistics(@Body() exportDto: TeacherStatisticsExportDTO) {
    try {
      this.ctx.logger.info('导出教师统计数据请求', {
        sso_teacher_id: exportDto.sso_teacher_id,
        sso_school_code: exportDto.sso_school_code,
        month: exportDto.month,
        export_format: exportDto.export_format,
      });

      exportDto.export_type = ExportType.TEACHER_STATISTICS;
      const result = await this.exportService.exportToExcel(exportDto);

      // 返回下载链接
      this.ctx.body = {
        errCode: 0,
        msg: '导出成功',
        data: {
          downloadUrl: result.downloadUrl,
          filename: result.filename,
          fileId: result.fileId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('导出教师统计数据失败', error, {
        sso_teacher_id: exportDto.sso_teacher_id,
      });

      this.ctx.status = 500;
      this.ctx.body = {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '导出失败',
        data: null,
      };
    }
  }

  /**
   * 导出教师排名数据
   */
  @Post('/teacher-ranking')
  @Validate()
  async exportTeacherRanking(@Body() exportDto: TeacherRankingExportDTO) {
    try {
      this.ctx.logger.info('导出教师排名数据请求', {
        sso_school_code: exportDto.sso_school_code,
        month: exportDto.month,
        subject: exportDto.subject,
        department: exportDto.department,
        export_format: exportDto.export_format,
      });

      exportDto.export_type = ExportType.TEACHER_RANKING;
      const result = await this.exportService.exportToExcel(exportDto);

      // 返回下载链接
      this.ctx.body = {
        errCode: 0,
        msg: '导出成功',
        data: {
          downloadUrl: result.downloadUrl,
          filename: result.filename,
          fileId: result.fileId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('导出教师排名数据失败', error, {
        sso_school_code: exportDto.sso_school_code,
      });

      this.ctx.status = 500;
      this.ctx.body = {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '导出失败',
        data: null,
      };
    }
  }

  /**
   * 导出问卷响应数据
   */
  @Post('/questionnaire-responses')
  @Validate()
  async exportQuestionnaireResponses(
    @Body() exportDto: QuestionnaireResponsesExportDTO
  ) {
    try {
      this.ctx.logger.info('导出问卷响应数据请求', {
        sso_school_code: exportDto.sso_school_code,
        questionnaire_id: exportDto.questionnaire_id,
        month: exportDto.month,
        export_format: exportDto.export_format,
      });

      exportDto.export_type = ExportType.QUESTIONNAIRE_RESPONSES;
      const result = await this.exportService.exportToExcel(exportDto);

      // 返回下载链接
      this.ctx.body = {
        errCode: 0,
        msg: '导出成功',
        data: {
          downloadUrl: result.downloadUrl,
          filename: result.filename,
          fileId: result.fileId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('导出问卷响应数据失败', error, {
        sso_school_code: exportDto.sso_school_code,
      });

      this.ctx.status = 500;
      this.ctx.body = {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '导出失败',
        data: null,
      };
    }
  }

  /**
   * 导出综合报表
   */
  @Post('/comprehensive-report')
  @Validate()
  async exportComprehensiveReport(
    @Body() exportDto: ComprehensiveReportExportDTO
  ) {
    try {
      this.ctx.logger.info('导出综合报表请求', {
        sso_school_code: exportDto.sso_school_code,
        month: exportDto.month,
        start_month: exportDto.start_month,
        end_month: exportDto.end_month,
        export_format: exportDto.export_format,
      });

      exportDto.export_type = ExportType.COMPREHENSIVE_REPORT;
      const result = await this.exportService.exportToExcel(exportDto);

      // 返回下载链接
      this.ctx.body = {
        errCode: 0,
        msg: '导出成功',
        data: {
          downloadUrl: result.downloadUrl,
          filename: result.filename,
          fileId: result.fileId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('导出综合报表失败', error, {
        sso_school_code: exportDto.sso_school_code,
      });

      this.ctx.status = 500;
      this.ctx.body = {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '导出失败',
        data: null,
      };
    }
  }

  /**
   * 导出未完成学生名单
   */
  @Post('/incomplete-students')
  @Validate()
  async exportIncompleteStudents(
    @Body() exportDto: IncompleteStudentsExportDTO
  ) {
    try {
      this.ctx.logger.info('导出未完成学生名单请求', {
        sso_school_code: exportDto.sso_school_code,
        questionnaire_id: exportDto.questionnaire_id,
        month: exportDto.month,
        export_format: exportDto.export_format,
      });

      exportDto.export_type = ExportType.INCOMPLETE_STUDENTS;
      const result = await this.exportService.exportToExcel(exportDto);

      // 返回下载链接
      this.ctx.body = {
        errCode: 0,
        msg: '导出成功',
        data: {
          downloadUrl: result.downloadUrl,
          filename: result.filename,
          fileId: result.fileId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('导出未完成学生名单失败', error, {
        sso_school_code: exportDto.sso_school_code,
      });

      this.ctx.status = 500;
      this.ctx.body = {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '导出失败',
        data: null,
      };
    }
  }

  /**
   * 通用导出接口
   */
  @Post('/general')
  @Validate()
  async exportGeneral(@Body() exportDto: BaseExportDTO) {
    try {
      this.ctx.logger.info('通用导出请求', {
        export_type: exportDto.export_type,
        sso_school_code: exportDto.sso_school_code,
        month: exportDto.month,
        export_format: exportDto.export_format,
      });

      const result = await this.exportService.exportToExcel(exportDto);

      // 返回下载链接
      this.ctx.body = {
        errCode: 0,
        msg: '导出成功',
        data: {
          downloadUrl: result.downloadUrl,
          filename: result.filename,
          fileId: result.fileId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('通用导出失败', error, {
        export_type: exportDto.export_type,
        sso_school_code: exportDto.sso_school_code,
      });

      this.ctx.status = 500;
      this.ctx.body = {
        errCode: ErrorCode.BIZ_ERROR,
        msg: error.message || '导出失败',
        data: null,
      };
    }
  }
}
