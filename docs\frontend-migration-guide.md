# 前端API迁移指南

## 📋 概述

统计模块已完成整合，所有统计功能现在通过统一的API提供。本文档说明前端需要进行的API调用变更。

## 🔄 API变更对比

### 实时统计API（无变更）
以下API保持不变，可以继续使用：

```javascript
// 学校统计
GET /api/statistics/school

// 教师统计  
GET /api/statistics/teacher

// 教师排名
GET /api/statistics/teacher-ranking

// 趋势分析
GET /api/statistics/trend

// 未填写学生列表（实时）
GET /api/statistics/incomplete-students
```

### 缓存统计API（路径简化）

#### 旧API → 新API
```javascript
// 触发统计计算
POST /api/statistics-task/trigger → POST /api/statistics/trigger

// 获取统计状态
GET /api/statistics-task/status/:id → GET /api/statistics/status/:id

// 获取缓存数据
GET /api/statistics-task/cached/:id → GET /api/statistics/cached/:id

// 获取未填写学生列表（缓存）
GET /api/statistics-task/incomplete-students → GET /api/statistics/cached-incomplete-students
```

## 🔧 前端代码修改示例

### 1. 触发统计计算

#### 修改前
```javascript
const response = await fetch('/api/statistics-task/trigger', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ questionnaire_id: 1 })
});
```

#### 修改后
```javascript
const response = await fetch('/api/statistics/trigger', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ questionnaire_id: 1 })
});
```

### 2. 查询统计状态

#### 修改前
```javascript
const response = await fetch(`/api/statistics-task/status/${questionnaireId}`);
```

#### 修改后
```javascript
const response = await fetch(`/api/statistics/status/${questionnaireId}`);
```

### 3. 获取缓存统计数据

#### 修改前
```javascript
const response = await fetch(`/api/statistics-task/cached/${questionnaireId}`);
```

#### 修改后
```javascript
const response = await fetch(`/api/statistics/cached/${questionnaireId}`);
```

### 4. 获取未填写学生列表

#### 修改前
```javascript
const response = await fetch('/api/statistics-task/incomplete-students?' +
  new URLSearchParams({
    questionnaire_id: 1,
    page: 1,
    pageSize: 20
  })
);
```

#### 修改后
```javascript
const response = await fetch('/api/statistics/cached-incomplete-students?' +
  new URLSearchParams({
    questionnaire_id: 1,
    page: 1,
    pageSize: 20
  })
);
```

## 📊 完整的API列表

### 实时统计API
```javascript
// 获取学校统计
GET /api/statistics/school?sso_school_code=xxx&month=2024-01

// 获取教师统计
GET /api/statistics/teacher?sso_teacher_id=xxx&month=2024-01

// 获取教师排名
GET /api/statistics/teacher-ranking?sso_school_code=xxx&page=1&limit=20

// 获取趋势分析
GET /api/statistics/trend?sso_school_code=xxx&start_month=2024-01&end_month=2024-03

// 获取未填写学生（实时）
GET /api/statistics/incomplete-students?sso_school_code=xxx&questionnaire_id=1
```

### 缓存统计API
```javascript
// 触发统计计算
POST /api/statistics/trigger
Body: { "questionnaire_id": 1 }

// 获取统计状态
GET /api/statistics/status/1

// 获取缓存统计数据
GET /api/statistics/cached/1

// 获取未填写学生（缓存）
GET /api/statistics/cached-incomplete-students?questionnaire_id=1&page=1&pageSize=20
```

## 🚀 使用建议

### 选择合适的API模式

#### 使用实时统计API的场景：
- 学校学生数量 < 1000
- 需要最新的实时数据
- 偶尔查询，对响应时间要求不高

#### 使用缓存统计API的场景：
- 学校学生数量 > 1000
- 频繁查询统计数据
- 对响应时间要求较高
- 可以接受手动触发更新

### 统计状态更新机制

缓存统计采用**异步计算**模式：

1. **触发计算**：调用 `/api/statistics/trigger` 后立即返回，状态为 `calculating`
2. **状态更新**：系统在后台异步执行统计计算
3. **计算完成**：状态更新为 `completed`，可以获取结果
4. **计算失败**：状态更新为 `failed`，包含错误信息

**状态值说明：**
- `calculating` - 正在计算中
- `completed` - 计算完成，可以获取数据
- `failed` - 计算失败
- `not_started` - 尚未开始计算

### 缓存统计使用流程

```javascript
// 1. 触发统计计算
const triggerResponse = await fetch('/api/statistics/trigger', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ questionnaire_id: 1 })
});

const triggerResult = await triggerResponse.json();
console.log('触发结果:', triggerResult);
// 返回: { errCode: 0, msg: "统计任务已启动", data: { status: "calculating", ... } }

// 2. 轮询查询状态（推荐间隔2-5秒）
const checkStatus = async () => {
  const statusResponse = await fetch('/api/statistics/status/1');
  const status = await statusResponse.json();

  console.log('当前状态:', status.data.status);

  if (status.data.status === 'completed') {
    // 3. 获取缓存数据
    const dataResponse = await fetch('/api/statistics/cached/1');
    const data = await dataResponse.json();
    console.log('统计数据:', data);
    return data;
  } else if (status.data.status === 'calculating') {
    // 继续等待（建议显示进度提示）
    console.log('计算中，请稍候...');
    setTimeout(checkStatus, 3000); // 3秒后再次检查
  } else if (status.data.status === 'failed') {
    // 处理错误
    console.error('统计计算失败:', status.data.error_message);
    throw new Error(status.data.error_message);
  }
};

checkStatus();
```

### 前端实现建议

#### 1. 显示计算进度
```javascript
// 在UI中显示计算状态
const showCalculationStatus = (status) => {
  const statusMap = {
    'calculating': '正在计算统计数据...',
    'completed': '计算完成',
    'failed': '计算失败',
    'not_started': '尚未开始计算'
  };

  document.getElementById('status').textContent = statusMap[status] || '未知状态';
};
```

#### 2. 错误处理
```javascript
const handleStatisticsError = (error) => {
  console.error('统计错误:', error);
  // 显示用户友好的错误信息
  alert('统计计算失败，请稍后重试');
};
```

#### 3. 避免重复触发
```javascript
let isCalculating = false;

const triggerStatistics = async (questionnaireId) => {
  if (isCalculating) {
    alert('统计正在计算中，请稍候');
    return;
  }

  isCalculating = true;
  try {
    // 触发计算逻辑
    await triggerCalculation(questionnaireId);
  } finally {
    isCalculating = false;
  }
};
```

## 📝 注意事项

### 1. 响应格式保持不变
所有API的响应格式保持不变，只是URL路径发生了变化。

### 2. 参数格式保持不变
所有API的请求参数格式保持不变。

### 3. 错误处理保持不变
错误响应的格式和错误码保持不变。

### 4. 分页参数统一
所有分页API统一使用 `page` 和 `pageSize` 参数。

## 🔍 快速替换方法

如果你的项目中有很多API调用，可以使用全局替换：

### 在代码中查找并替换：
```
查找: /api/statistics-task/
替换为: /api/statistics/
```

### 具体替换规则：
```
/api/statistics-task/trigger → /api/statistics/trigger
/api/statistics-task/status/ → /api/statistics/status/
/api/statistics-task/cached/ → /api/statistics/cached/
/api/statistics-task/incomplete-students → /api/statistics/cached-incomplete-students
```

## ✅ 迁移检查清单

- [ ] 更新触发统计计算的API调用
- [ ] 更新查询统计状态的API调用  
- [ ] 更新获取缓存数据的API调用
- [ ] 更新获取未填写学生列表的API调用
- [ ] 测试所有统计功能是否正常工作
- [ ] 验证错误处理是否正常
- [ ] 检查分页功能是否正常

完成以上检查后，前端就可以正常使用新的统计API了！
