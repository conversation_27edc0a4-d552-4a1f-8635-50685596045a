#!/bin/bash

# 115环境部署脚本
# 动态查找git路径
git_path=$(command -v git)
if [ -z "$git_path" ]; then
  echo "Error: git command not found in the system."
  exit 1
fi

## 更新代码
echo "更新代码。。。"
"$git_path" pull
echo "开始构建(生产模式)。。。"
npm install            # 安装开发期依赖
npm run build          # 构建项目
npm prune --production # 移除开发依赖

pm2_name="teacherEvaluation_question"

# 检查服务是否存在
service_name=$(pm2 jlist | grep -oP "(?<=\"name\":\")[^\"]*")
echo $service_name
if echo "$service_name" | grep -q "${pm2_name}"; then
  echo "服务已存在，重启"
  pm2 restart ${pm2_name}
else
  echo "服务不存在，创建并启动"
  NODE_ENV=production pm2 start ./bootstrap.js --name ${pm2_name}
fi
