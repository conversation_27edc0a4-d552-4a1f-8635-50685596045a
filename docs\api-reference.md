# API 接口参考文档

## 📡 接口总览

教师评价问卷系统提供了完整的REST API接口，涵盖问卷管理、响应提交和统计分析三大功能模块。

### 🌐 基础信息

- **Base URL**: `http://localhost:3141`
- **Content-Type**: `application/json`
- **响应格式**: 统一JSON格式

### 📋 统一响应格式

```json
{
  "errCode": 0,           // 错误码，0表示成功
  "msg": "操作成功",       // 响应消息
  "data": {}              // 响应数据
}
```

## 🗂️ 问卷管理模块 API

### 1. 创建问卷

```http
POST /api/questionnaire
Content-Type: application/json

{
  "title": "2024年1月教师评价问卷",
  "description": "请对本月教师表现进行评价",
  "month": "2024-01",
  "sso_school_code": "school_001",
  "star_mode": 5,
  "include_school_evaluation": true,
  "instructions": "请客观公正地评价",
  "allow_anonymous": false,
  "max_teachers_limit": 10,
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-31T23:59:59Z"
}
```

### 2. 获取问卷列表

```http
GET /api/questionnaire?page=1&limit=10&sso_school_code=school_001&status=published&month=2024-01
```

**查询参数：**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `sso_school_code`: 学校ID（可选）
- `status`: 问卷状态（可选）
- `month`: 月份（可选）

### 3. 获取问卷详情

```http
GET /api/questionnaire/{id}
```

### 4. 更新问卷状态

```http
PUT /api/questionnaire/{id}/status
Content-Type: application/json

{
  "status": "published"
}
```

### 5. 删除问卷

```http
DELETE /api/questionnaire/{id}
```

## 📝 问卷填写模块 API

### 1. 提交问卷响应

```http
POST /api/response
Content-Type: application/json

{
  "questionnaire_id": 1,
  "parent_phone": "13800138000",
  "parent_name": "张三家长",
  "sso_student_code": "001",
  "month": "2024-01",
  "school_rating": 85,
  "school_description": "学校整体表现很好",
  "teacher_evaluations": [
    {
      "sso_teacher_id": "teacher_001",
      "rating": 88,
      "description": "老师教学认真负责"
    }
  ],
  "remarks": "整体评价很好"
}
```

### 2. 获取响应列表

```http
GET /api/response?page=1&limit=10&questionnaire_id=1&is_completed=true
```

**查询参数：**
- `questionnaire_id`: 问卷ID（可选）
- `parent_phone`: 家长手机号（可选）
- `sso_student_code`: 学生Code（可选）
- `month`: 月份（可选）
- `is_completed`: 是否完成（可选）
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）

### 3. 获取响应详情

```http
GET /api/response/{id}
```

### 4. 检查重复提交

```http
GET /api/response/check?parent_phone=13800138000&questionnaire_id=1&sso_student_code=student_001&month=2024-01
```

### 5. 获取问卷统计信息

```http
GET /api/response/statistics/{questionnaireId}
```

### 6. 获取评分转换信息

```http
GET /api/response/rating-info/{questionnaireId}
```

## 📊 统计分析模块 API

### 学校维度统计

#### 1. 获取学校统计概览

```http
GET /api/statistics/school?sso_school_code=school_001&month=2024-01&include_trend=true&include_teacher_ranking=true
```

**查询参数：**
- `sso_school_code`: 学校ID（必填）
- `month`: 月份（可选）
- `start_month`: 开始月份（可选）
- `end_month`: 结束月份（可选）
- `include_trend`: 是否包含趋势数据（可选）
- `include_teacher_ranking`: 是否包含教师排名（可选）

#### 2. 获取学校响应趋势

```http
GET /api/statistics/school/{schoolId}/trend?start_month=2024-01&end_month=2024-03
```

#### 3. 获取教师排名

```http
GET /api/statistics/teacher-ranking?sso_school_code=school_001&month=2024-01&sort_by=average_score&sort_order=DESC&page=1&limit=20
```

**查询参数：**
- `sso_school_code`: 学校ID（必填）
- `month`: 月份（可选）
- `subject`: 科目筛选（可选）
- `department`: 部门筛选（可选）
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `sort_by`: 排序字段（average_score/evaluation_count/recommendation_rate）
- `sort_order`: 排序方向（ASC/DESC）

#### 4. 获取趋势分析

```http
GET /api/statistics/trend?sso_school_code=school_001&start_month=2024-01&end_month=2024-03&analysis_type=school
```

### 教师维度统计

#### 1. 获取教师统计概览

```http
GET /api/statistics/teacher?sso_teacher_id=teacher_001&sso_school_code=school_001&month=2024-01&include_distribution=true&include_keywords=true&include_trend=true
```

**查询参数：**
- `sso_teacher_id`: 教师ID（必填）
- `sso_school_code`: 学校ID（可选）
- `month`: 月份（可选）
- `start_month`: 开始月份（可选）
- `end_month`: 结束月份（可选）
- `include_distribution`: 是否包含评分分布（可选）
- `include_keywords`: 是否包含关键词云（可选）
- `include_trend`: 是否包含趋势数据（可选）

#### 2. 获取教师评分分布

```http
GET /api/statistics/teacher/{teacherId}/distribution?sso_school_code=school_001&month=2024-01
```

#### 3. 获取教师关键词云

```http
GET /api/statistics/teacher/{teacherId}/keywords?sso_school_code=school_001&month=2024-01
```

#### 4. 获取教师评价趋势

```http
GET /api/statistics/teacher/{teacherId}/trend?sso_school_code=school_001&start_month=2024-01&end_month=2024-03
```

## 📋 完整接口清单

| 模块 | 接口名称 | 方法 | 路径 | 功能描述 |
|------|----------|------|------|----------|
| **问卷管理** | 创建问卷 | POST | `/api/questionnaire` | 创建新的问卷 |
| | 获取问卷列表 | GET | `/api/questionnaire` | 分页获取问卷列表 |
| | 获取问卷详情 | GET | `/api/questionnaire/:id` | 根据ID获取问卷详情 |
| | 更新问卷状态 | PUT | `/api/questionnaire/:id/status` | 更新问卷状态 |
| | 删除问卷 | DELETE | `/api/questionnaire/:id` | 删除问卷 |
| **问卷填写** | 提交问卷响应 | POST | `/api/response` | 家长提交教师评价 |
| | 获取响应列表 | GET | `/api/response` | 分页获取响应列表 |
| | 获取响应详情 | GET | `/api/response/:id` | 根据ID获取响应详情 |
| | 检查重复提交 | GET | `/api/response/check` | 检查是否已提交响应 |
| | 获取问卷统计 | GET | `/api/response/statistics/:id` | 获取问卷统计信息 |
| | 获取评分信息 | GET | `/api/response/rating-info/:id` | 获取评分系统说明信息 |
| **学校统计** | 学校统计概览 | GET | `/api/statistics/school` | 获取学校整体统计数据 |
| | 学校响应趋势 | GET | `/api/statistics/school/:schoolId/trend` | 获取学校响应趋势数据 |
| | 教师排名 | GET | `/api/statistics/teacher-ranking` | 获取教师评分排名 |
| | 趋势分析 | GET | `/api/statistics/trend` | 获取趋势分析数据 |
| **教师统计** | 教师统计概览 | GET | `/api/statistics/teacher` | 获取教师个人统计数据 |
| | 教师评分分布 | GET | `/api/statistics/teacher/:teacherId/distribution` | 获取教师评分分布 |
| | 教师关键词云 | GET | `/api/statistics/teacher/:teacherId/keywords` | 获取教师评价关键词 |
| | 教师评价趋势 | GET | `/api/statistics/teacher/:teacherId/trend` | 获取教师评价趋势 |

## 🔧 错误码说明

| 错误码 | 说明 | 示例 |
|--------|------|------|
| 0 | 成功 | 操作成功 |
| 400 | 业务错误 | 问卷不存在、重复提交等 |
| 401 | 参数错误 | 必填参数缺失、格式错误等 |
| 500 | 系统错误 | 数据库连接失败、服务异常等 |

## 🧪 测试示例

### 使用 curl 测试

```bash
# 创建问卷
curl -X POST http://localhost:3141/api/questionnaire \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试问卷",
    "month": "2024-01",
    "sso_school_code": "school_001",
    "star_mode": 5
  }'

# 获取学校统计
curl "http://localhost:3141/api/statistics/school?sso_school_code=school_001&month=2024-01"

# 获取教师排名
curl "http://localhost:3141/api/statistics/teacher-ranking?sso_school_code=school_001&limit=10"
```

### 使用 JavaScript 测试

```javascript
// 获取学校统计数据
const getSchoolStats = async () => {
  const response = await fetch('/api/statistics/school?sso_school_code=school_001&month=2024-01');
  const result = await response.json();
  console.log(result);
};

// 提交问卷响应
const submitResponse = async (data) => {
  const response = await fetch('/api/response', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  const result = await response.json();
  console.log(result);
};
```

---

**📚 更多详细信息请参考各模块的专门文档。**
