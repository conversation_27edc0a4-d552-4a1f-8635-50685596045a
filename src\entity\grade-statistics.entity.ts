import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { QuestionnaireStatistics } from './questionnaire-statistics.entity';

@Table({
  tableName: 'grade_statistics',
  comment: '年级统计表',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class GradeStatistics extends Model<GradeStatistics> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '主键ID',
  })
  id: number;

  @ForeignKey(() => QuestionnaireStatistics)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '主统计表ID',
  })
  statistics_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '问卷ID',
  })
  questionnaire_id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学校编码',
  })
  sso_school_code: string;

  @Column({
    type: DataType.STRING(10),
    allowNull: false,
    comment: '年级编码',
  })
  grade_code: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '年级名称',
  })
  grade_name: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '年级总学生数',
  })
  total_students: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '已提交数量',
  })
  submitted_count: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '完成率(%)',
  })
  completion_rate: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '年级平均分',
  })
  average_score: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '创建时间',
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '更新时间',
  })
  updated_at: Date;

  // 关联关系
  @BelongsTo(() => QuestionnaireStatistics)
  statistics: QuestionnaireStatistics;
}
