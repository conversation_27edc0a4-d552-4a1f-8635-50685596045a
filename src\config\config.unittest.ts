import { MidwayConfig } from '@midwayjs/core';

export default {
  // 测试环境使用不同的端口
  koa: {
    port: 3142, // 避免与开发环境端口冲突
  },
  sequelize: {
    dataSource: {
      default: {
        // 测试环境优先使用远程MySQL数据库（config.local中的配置）
        dialect: 'mysql',
        host: '*************',
        port: 3306,
        username: 'root',
        password: 'Clouddeep@8890',
        database: 'teacher-evaluation-question', // 使用开发数据库进行测试
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 2, // 测试环境减少连接数
          min: 0,
          idle: 5000,
          acquire: 10000, // 获取连接超时时间
          evict: 1000, // 检查空闲连接间隔
        },
        entities: ['entity'],
        logging: false,
        repositoryMode: true,
        sync: {
          force: false, // 不强制重建表，避免数据丢失
          alter: true, // 允许修改表结构
        },
        // 连接重试配置
        retry: {
          max: 3,
          timeout: 5000,
        },
      },
    },
  },
  // JWT认证组件配置
  jwtAuth: {
    enable: true,
    // 测试环境使用远程API
    apiManagerBaseURL: 'http://*************:1002',
    // 项目特定的白名单路径（会与默认白名单合并）
    whitelist: [
      '/api/parent', // 家长相关接口
      '/api/response', // 问卷填写相关接口
      '/api/sso', // SSO集成接口
    ],
  },
} as MidwayConfig;
