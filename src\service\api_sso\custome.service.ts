/*
 * @Description: 对接客户系统相关接口
 * @Date: 2025-03-29 10:17:13
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-06-24 15:41:46
 */
import { Inject, Provide } from '@midwayjs/core';
import { APIManager } from './index.service';
import { IStudent, IUserInfo, ParentInfo } from './interface';

type GetStudentPapras = {
  /** 学校编号，必填 */
  enterpriseCode: string;
  /** 学期编号，不传则使用当前激活的学期 */
  semesterCode?: string;
  /** 年级编号 */
  grade?: string;
  /** 班级编号 */
  classCode?: string;
  /** 学生状态，多个用英文逗号分割，不传则只查询在读学生 */
  status?: string;
};

@Provide()
export class Custome {
  @Inject()
  apiManager: APIManager;

  /**
   * 根据学校编号查询学校信息
   * @param enterpriseCode 学校编号
   * @returns 学校信息
   */
  async getEnterpriseByCode(enterpriseCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getEnterpriseByCode',
      params: enterpriseCode,
    });
    return result;
  }

  /**
   * 查询学期列表
   * @param enterpriseCode 学校编号
   * @returns 学期列表
   */
  async getSemesterList(enterpriseCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getSemesterList',
      params: enterpriseCode,
    });
    return result;
  }

  /**
   * 查询角色列表
   * @param enterpriseCode 学校编号
   * @returns 角色列表
   * */
  async getSchoolRoles(enterpriseCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getSchoolRoles',
      query: { enterpriseCode },
    });
    return { list: result.list };
  }

  /**
   * 查询角色下的用户列表
   * @param enterpriseCode 学校编号
   * @param roleCode 角色编号
   * @param limit 每页数量
   * @param offset 页码
   * @returns 用户列表
   * */
  async getSchoolUserRoleList(
    enterpriseCode: string,
    roleCode: string,
    limit?: number,
    offset?: number
  ): Promise<{ count: number; list: IUserInfo[] }> {
    const result = await this.apiManager.send({
      apiCode: 'getSchoolUserRoleList',
      query: { enterpriseCode, roleCode, limit, offset },
    });
    return result;
  }

  /**
   * 获取指定学校下的所有成员
   *
   * @param {string} enterpriseCode 学校编号
   * @return {*} list 成员列表
   * @memberof Custome
   */
  async getMembers(
    enterpriseCode: string
  ): Promise<{ count: number; list: IUserInfo[] }> {
    const result = await this.apiManager.send({
      apiCode: 'getMembers',
      query: { enterpriseCode },
    });
    return result;
  }

  /**
   * 获取班级教师通讯录
   * @param enterpriseCode 学校编号
   * @param gradeCode 年级编号
   * @param classCode 班级编号
   * @returns 教师列表
   */
  async getTeacherListForClass(
    enterpriseCode: string,
    gradeCode: string,
    classCode: string
  ) {
    const result = await this.apiManager.send({
      apiCode: 'getTeacherListForClass',
      params: enterpriseCode,
      query: { gradeCode, classCode },
    });
    return result as {
      name: string;
      code: string;
      teacherCode: string;
      type: string;
      courseName: string;
      courseCode: string;
    }[];
  }

  /**
   * 根据用户编号获取用户信息
   *
   * @param {string} enterpriseCode 学校编号
   * @param {string} code 用户编号
   * @return {*} 用户信息
   * @memberof Custome
   */
  async getMemberByCode(
    enterpriseCode: string,
    code: string
  ): Promise<IUserInfo> {
    const result = await this.apiManager.send({
      apiCode: 'getMemberByCode',
      params: `${enterpriseCode}/${code}`,
    });
    return result;
  }

  /**
   * 获取学生列表
   *
   * @param params 查询参数
   * @param params.enterpriseCode 学校编号
   * @param [params.semesterCode] 学期编号
   * @param [params.grade] 年级编号
   * @param [params.classCode] 班级编号
   * @param [params.status] 学生状态，多个用英文逗号分割，不传则只查询在读学生
   * @returns 学生列表
   */
  async getStudents(params: GetStudentPapras): Promise<IStudent[]> {
    const result = await this.apiManager.send({
      apiCode: 'getStudents',
      query: params,
    });
    return (result as { count: number; list: IStudent[] }).list;
  }

  /**
   * 获取指定家长手机号下的所有学生信息
   *
   * @param {string} mobile 家长手机号
   * @return {*} list 学生列表
   * @memberof Custome
   */
  async getChildren(mobile: string): Promise<ParentInfo> {
    const result = await this.apiManager.send({
      apiCode: 'getChildren',
      query: { mobile },
    });
    return result;
  }

  /**
   * 获取指定学校的指定学生信息
   *
   * @param {string} enterpriseId 学校id，注意目前custome没有提供通过学校编号查询学生接口
   * @param {string} code 学生编号
   * @param {string} name 学生姓名
   * @return {*} 学生信息
   * @memberof Custome
   */
  async getStudentInfo(enterpriseId: string, code: string, name: string) {
    const result = await this.apiManager.send({
      apiCode: 'getStudent',
      query: { enterpriseId, code, name },
    });
    return result;
  }
}
