import { Controller, Get, Inject, Param } from '@midwayjs/core';
import { QuestionnaireService } from '../service/questionnaire.service';
import { CourseService } from '../service/course.service';
import { ApiResponseUtil } from '../common/ApiResponse';
import { CustomError } from '../error/custom.error';

/**
 * 公开接口控制器（无需认证）
 */
@Controller('/public')
export class PublicController {
  @Inject()
  questionnaireService: QuestionnaireService;

  @Inject()
  courseService: CourseService;

  /**
   * 获取指定学校近1年内的问卷列表
   * @param schoolCode 学校编码
   */
  @Get('/questionnaire/school/:schoolCode')
  async getQuestionnairesBySchool(@Param('schoolCode') schoolCode: string) {
    try {
      if (!schoolCode) {
        throw new CustomError('学校编码不能为空');
      }

      const questionnaires =
        await this.questionnaireService.getPublicQuestionnairesBySchool(
          schoolCode
        );

      return ApiResponseUtil.success(questionnaires, '获取问卷列表成功');
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('获取问卷列表失败: ' + error.message);
    }
  }

  /**
   * 获取指定问卷的教师成绩统计
   * @param questionnaireId 问卷ID
   */
  @Get('/questionnaire/:questionnaireId/teacher-scores')
  async getTeacherScoresByQuestionnaire(
    @Param('questionnaireId') questionnaireId: number
  ) {
    try {
      if (!questionnaireId || isNaN(questionnaireId)) {
        throw new CustomError('问卷ID无效');
      }

      const teacherScores =
        await this.questionnaireService.getPublicTeacherScoresByQuestionnaire(
          questionnaireId
        );

      return ApiResponseUtil.success(teacherScores, '获取教师成绩统计成功');
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('获取教师成绩统计失败: ' + error.message);
    }
  }

  /**
   * 获取指定学校的所有课程列表
   * @param schoolCode 学校编码
   */
  @Get('/courses/school/:schoolCode')
  async getSchoolCourses(@Param('schoolCode') schoolCode: string) {
    try {
      if (!schoolCode || schoolCode.trim() === '') {
        throw new CustomError('学校编码不能为空');
      }

      const courses = await this.courseService.getSchoolCourses(
        schoolCode.trim()
      );

      return ApiResponseUtil.success(courses, '获取学校课程列表成功');
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('获取学校课程列表失败: ' + error.message);
    }
  }
}
